#!/usr/bin/env python3
"""
Example script showing how to modify the data loading for local environment
"""

import tensorflow as tf
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import os

print("Setting up MobileNetV2 for bird classification...")

# Define the input shape and number of classes
input_shape = (224, 224, 3)
num_classes = 4

# Define data generators with additional data augmentation
datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
    validation_split=0.3,
)

# For local environment, you need to specify your local data directory
# Replace this path with your actual data directory
# Example structure:
# data/
#   ├── Eurasian Tree Sparrow - Passer montanus/
#   ├── Javan Munia - Lonchura leucogastroides/
#   ├── Scaly-breasted Munia - Lonchura punctulata/
#   └── White-headed Munia - Lonchura maja/

# MODIFY THIS PATH TO YOUR LOCAL DATA DIRECTORY
main_data_dir = './data'  # Change this to your actual data path

# Check if data directory exists
if not os.path.exists(main_data_dir):
    print(f"⚠️  Data directory '{main_data_dir}' not found!")
    print("Please create the data directory with the following structure:")
    print("data/")
    print("  ├── Eurasian Tree Sparrow - Passer montanus/")
    print("  ├── Javan Munia - Lonchura leucogastroides/")
    print("  ├── Scaly-breasted Munia - Lonchura punctulata/")
    print("  └── White-headed Munia - Lonchura maja/")
    print("\nEach subdirectory should contain images of the respective bird species.")
else:
    print(f"✓ Data directory found: {main_data_dir}")
    
    # List subdirectories (classes)
    classes = [d for d in os.listdir(main_data_dir) 
               if os.path.isdir(os.path.join(main_data_dir, d))]
    print(f"Found {len(classes)} classes:")
    for cls in classes:
        class_path = os.path.join(main_data_dir, cls)
        image_count = len([f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
        print(f"  - {cls}: {image_count} images")

# Set batch size
batch_size = 32  # Reduced for local environment

print("\nSetting up MobileNetV2 model...")

# Load MobileNetV2 as base model
base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)

# Freeze layers in base model
for layer in base_model.layers:
    layer.trainable = False

# Add custom head
x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
x = Dense(1024, activation='relu')(x)
x = Dropout(0.5)(x)
predictions = Dense(num_classes, activation='softmax')(x)

# Combine base model and custom head
model = Model(inputs=base_model.input, outputs=predictions)

# Fine-tune the model by unfreezing some layers
for layer in base_model.layers[:-10]:
    layer.trainable = False

# Implement learning rate scheduling
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate=1e-4,
    decay_steps=1000,
    decay_rate=0.9
)
optimizer = Adam(learning_rate=lr_schedule)

# Compile the model
model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])

print("✓ Model compiled successfully!")
print(f"Model has {model.count_params():,} parameters")

# Display model summary
print("\nModel Summary:")
model.summary()

print("\n" + "="*50)
print("SETUP COMPLETE!")
print("="*50)
print("\nTo use this model:")
print("1. Create a 'data' directory in this folder")
print("2. Add subdirectories for each bird class")
print("3. Place images in the respective class directories")
print("4. Run the training code from your notebook")
print("\nAll required packages are now installed and working!")
