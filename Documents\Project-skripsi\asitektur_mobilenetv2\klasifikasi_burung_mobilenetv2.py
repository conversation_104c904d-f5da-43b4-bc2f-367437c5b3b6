# -*- coding: utf-8 -*-
"""Klasif<PERSON><PERSON>_burung_mobilenetv2.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1yaLUA-nJ5hkhmaWEqq3rGfyK0RkwiofP
"""

import tensorflow as tf
import zipfile
import os
from google.colab import drive
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix

"""# Define the input shape and number of classes"""

input_shape = (224, 224, 3)
num_classes = 5

"""# Define data generators with additional data augmentation"""

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
)

valid_datagen = ImageDataGenerator(
    rescale=1./255
)

#input data
drive.mount('/content/drive')
# Salin file zip dari Drive ke penyimpanan lokal Colab (jauh lebih cepat)
!cp "/content/drive/MyDrive/images.zip" "/content/"

# Ekstrak file zip di penyimpanan lokal
import zipfile
with zipfile.ZipFile("/content/images.zip", 'r') as zip_ref:
    zip_ref.extractall("/content/dataset") # Ekstrak ke folder /content/dataset

# Definisikan ulang path direktori Anda ke folder lokal
train_data_dir = '/content/dataset/images/train'
valid_data_dir = '/content/dataset/images/validation'

# Set batch size
batch_size = 128


# Define train generator (tanpa subset parameter)
train_generator = train_datagen.flow_from_directory(
    train_data_dir,  # Path ke direktori training
    target_size=(224, 224),
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=True
)

# Define validation generator (tanpa subset parameter)
valid_generator = valid_datagen.flow_from_directory(
    valid_data_dir,  # Path ke direktori validation
    target_size=(224, 224),
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=False
)

"""# Load MobileNetV2 as base model"""

base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)

"""# Freeze layers in base model"""

for layer in base_model.layers:
    layer.trainable = False

"""# Add custom head"""

x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
x = Dense(1024, activation='relu')(x)
x = Dropout(0.5)(x)
predictions = Dense(num_classes, activation='softmax')(x)

# Combine base model and custom head
model = Model(inputs=base_model.input, outputs=predictions)

# Fine-tune the model by unfreezing some layers
for layer in base_model.layers[:-10]:
    layer.trainable = False

# Implement learning rate scheduling
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate=1e-4,
    decay_steps=1000,
    decay_rate=0.9
)
optimizer = Adam(learning_rate=lr_schedule)

"""# Compile the model with class weights"""

model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])

# Implement early stopping callback
early_stopping = tf.keras.callbacks.EarlyStopping(
    monitor='val_loss',
    patience=3,
    restore_best_weights=True
)

# Train the model with modified settings
history = model.fit(
    train_generator,
    epochs=30,  # Increase epochs for better training
    validation_data=valid_generator,
    callbacks=[early_stopping],
    verbose=1
)

"""# Evaluate the model on test data"""

test_loss, test_accuracy = model.evaluate(valid_generator)
print(f"Test Loss: {test_loss:.4f}")
print(f"Test Accuracy: {test_accuracy:.4f}")

# Define the path to the TEST directory
test_dir = '/content/dataset/images/test' # Ganti nama path jika folder Anda berbeda

# Define the data generator for the TEST data
test_datagen = ImageDataGenerator(rescale=1./255)

# Load the TEST data using the ImageDataGenerator
test_generator = test_datagen.flow_from_directory(
    test_dir,
    target_size=(224, 224),
    batch_size=batch_size, # Sebaiknya set batch_size=1 atau 32 untuk evaluasi
    class_mode='categorical',
    shuffle=False  # Sangat penting untuk set shuffle=False saat evaluasi
)

# Define the class labels
class_labels = list(test_generator.class_indices.keys())

# Make predictions on the TEST data
test_predictions = model.predict(test_generator)
predicted_labels = np.argmax(test_predictions, axis=1)

# Get the true labels for the TEST data
true_labels = test_generator.classes

# Calculate confusion matrix for TEST data
conf_matrix = confusion_matrix(true_labels, predicted_labels)

# Plot the confusion matrix
plt.figure(figsize=(10, 5))
sns.heatmap(conf_matrix, annot=True, fmt='d', xticklabels=class_labels, yticklabels=class_labels, cmap='Reds')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title('Confusion Matrix on Test Data')
plt.xticks(rotation=90)
plt.show()

"""# Plot confusion matrix

# Save the model
"""

model.save('birds_classification.h5')

from IPython.display import FileLink
FileLink(r'birds_classification.h5')