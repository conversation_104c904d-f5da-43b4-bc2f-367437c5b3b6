{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "execution": {"iopub.execute_input": "2024-06-13T09:01:34.171117Z", "iopub.status.busy": "2024-06-13T09:01:34.170710Z", "iopub.status.idle": "2024-06-13T09:01:46.493077Z", "shell.execute_reply": "2024-06-13T09:01:46.492271Z", "shell.execute_reply.started": "2024-06-13T09:01:34.171085Z"}, "trusted": true, "id": "qUBuU6fde2-r"}, "outputs": [], "source": ["# Import libraries untuk environment lokal\n", "import tensorflow as tf\n", "from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.applications import MobileNetV2\n", "from tensorflow.keras.models import Model\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from sklearn.utils.class_weight import compute_class_weight\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import confusion_matrix\n", "import os\n", "import cv2\n", "from PIL import Image\n", "from pathlib import Path\n", "\n", "print(\"✅ Libraries imported successfully for local environment!\")\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "preprocessing-section"}, "source": ["# Image Preprocessing Functions untuk 224x224 Input"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "preprocessing-functions"}, "outputs": [], "source": ["def resize_with_padding(image, target_size=(224, 224), pad_color=(0, 0, 0)):\n", "    \"\"\"\n", "    Resize gambar dengan padding untuk mempertahankan aspect ratio\n", "    \n", "    Args:\n", "        image: numpy array atau PIL Image\n", "        target_size: tuple (width, height) target size\n", "        pad_color: tuple (R, G, B) warna padding\n", "    \n", "    Returns:\n", "        numpy array gambar yang sudah diresize dengan padding\n", "    \"\"\"\n", "    if isinstance(image, np.ndarray):\n", "        # Convert numpy array to PIL Image\n", "        if len(image.shape) == 3:\n", "            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))\n", "        else:\n", "            image = Image.fromarray(image)\n", "    \n", "    # Get original dimensions\n", "    original_width, original_height = image.size\n", "    target_width, target_height = target_size\n", "    \n", "    # Calculate scaling factor to fit image within target size\n", "    scale = min(target_width / original_width, target_height / original_height)\n", "    \n", "    # Calculate new dimensions\n", "    new_width = int(original_width * scale)\n", "    new_height = int(original_height * scale)\n", "    \n", "    # Resize image\n", "    resized_image = image.resize((new_width, new_height), Image.LANCZOS)\n", "    \n", "    # Create new image with target size and padding color\n", "    new_image = Image.new('RGB', target_size, pad_color)\n", "    \n", "    # Calculate position to paste resized image (center it)\n", "    paste_x = (target_width - new_width) // 2\n", "    paste_y = (target_height - new_height) // 2\n", "    \n", "    # Paste resized image onto padded background\n", "    new_image.paste(resized_image, (paste_x, paste_y))\n", "    \n", "    return np.array(new_image)\n", "\n", "def resize_with_stretch(image, target_size=(224, 224)):\n", "    \"\"\"\n", "    Resize gambar dengan stretching (tanpa mempertahankan aspect ratio)\n", "    \n", "    Args:\n", "        image: numpy array atau PIL Image\n", "        target_size: tuple (width, height) target size\n", "    \n", "    Returns:\n", "        numpy array gambar yang sudah diresize\n", "    \"\"\"\n", "    if isinstance(image, np.ndarray):\n", "        # Convert numpy array to PIL Image\n", "        if len(image.shape) == 3:\n", "            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))\n", "        else:\n", "            image = Image.fromarray(image)\n", "    \n", "    # Resize dengan LANCZOS untuk kualitas terbaik\n", "    resized_image = image.resize(target_size, Image.LANCZOS)\n", "    \n", "    return np.array(resized_image)\n", "\n", "def preprocess_dataset(input_dir, output_dir, target_size=(224, 224), method='padding'):\n", "    \"\"\"\n", "    Preprocess seluruh dataset dengan mengkonversi semua gambar ke target size\n", "    \n", "    Args:\n", "        input_dir: path ke direktori input yang berisi subdirektori kelas\n", "        output_dir: path ke direktori output\n", "        target_size: tuple (width, height) target size\n", "        method: 'padding' atau 'stretch'\n", "    \"\"\"\n", "    input_path = Path(input_dir)\n", "    output_path = Path(output_dir)\n", "    \n", "    # Create output directory if it doesn't exist\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Supported image extensions\n", "    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}\n", "    \n", "    total_processed = 0\n", "    class_counts = {}\n", "    \n", "    print(f\"🔄 Memulai preprocessing gambar...\")\n", "    print(f\"📁 Input: {input_dir}\")\n", "    print(f\"📁 Output: {output_dir}\")\n", "    print(f\"🎯 Target size: {target_size}\")\n", "    print(f\"⚙️  Method: {method}\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Process each class directory\n", "    for class_dir in input_path.iterdir():\n", "        if class_dir.is_dir():\n", "            print(f\"📂 Processing class: {class_dir.name}\")\n", "            \n", "            # Create output class directory\n", "            output_class_dir = output_path / class_dir.name\n", "            output_class_dir.mkdir(exist_ok=True)\n", "            \n", "            # Get all image files\n", "            image_files = [f for f in class_dir.iterdir() \n", "                          if f.suffix.lower() in image_extensions]\n", "            \n", "            class_count = 0\n", "            \n", "            for i, image_file in enumerate(image_files):\n", "                try:\n", "                    # Load image\n", "                    image = cv2.imread(str(image_file))\n", "                    if image is None:\n", "                        continue\n", "                    \n", "                    # Convert BGR to RGB\n", "                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "                    \n", "                    # Apply preprocessing\n", "                    if method == 'padding':\n", "                        processed_image = resize_with_padding(image, target_size)\n", "                    else:\n", "                        processed_image = resize_with_stretch(image, target_size)\n", "                    \n", "                    # Save processed image\n", "                    output_file = output_class_dir / f\"{image_file.stem}.jpg\"\n", "                    \n", "                    # Convert to PIL and save\n", "                    pil_image = Image.fromarray(processed_image)\n", "                    pil_image.save(str(output_file), 'JPEG', quality=95)\n", "                    \n", "                    total_processed += 1\n", "                    class_count += 1\n", "                    \n", "                    if (i + 1) % 10 == 0:\n", "                        print(f\"  Processed {i + 1}/{len(image_files)} images\")\n", "                        \n", "                except Exception as e:\n", "                    print(f\"❌ Error processing {image_file}: {e}\")\n", "            \n", "            class_counts[class_dir.name] = class_count\n", "            print(f\"✅ {class_dir.name}: {class_count} images processed\")\n", "    \n", "    print(\"-\" * 50)\n", "    print(f\"🎉 Preprocessing selesai!\")\n", "    print(f\"📊 Total images processed: {total_processed}\")\n", "    print(\"📈 Class distribution:\")\n", "    for class_name, count in class_counts.items():\n", "        print(f\"   - {class_name}: {count} images\")\n", "    \n", "    return class_counts\n"]}, {"cell_type": "markdown", "metadata": {"id": "rOedQdnPe2-s"}, "source": ["# Define the input shape and number of classes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:01:46.495858Z", "iopub.status.busy": "2024-06-13T09:01:46.495191Z", "iopub.status.idle": "2024-06-13T09:01:46.500275Z", "shell.execute_reply": "2024-06-13T09:01:46.499443Z", "shell.execute_reply.started": "2024-06-13T09:01:46.495822Z"}, "trusted": true, "id": "ErWiDM50e2-u"}, "outputs": [], "source": ["input_shape = (224, 224, 3)\n", "num_classes = 4"]}, {"cell_type": "markdown", "metadata": {"id": "FyG_sydDe2-v"}, "source": ["# Define data generators with additional data augmentation"]}, {"cell_type": "markdown", "metadata": {"id": "data-preprocessing-section"}, "source": ["# Data Preprocessing - Konversi Gambar ke 224x224"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data-preprocessing-cell"}, "outputs": [], "source": ["# ===== KONFIGURASI DATA LOKAL =====\n", "# Script ini akan otomatis mendeteksi dan menggunakan data yang sudah ada\n", "\n", "print(\"🔍 Mendeteksi dataset yang tersedia...\")\n", "\n", "# Prioritas direktori data (dari yang paling diutamakan)\n", "data_options = [\n", "    ('./ResizedDataset', 'Data yang sudah diproses ke 224x224 - RECOMMENDED'),\n", "    ('./Dataset', 'Data asli dengan berbagai ukuran'),\n", "    ('./data', 'Direktori data alternatif'),\n", "    ('./processed_data', 'Data hasil preprocessing')\n", "]\n", "\n", "selected_data_dir = None\n", "dataset_info = {}\n", "\n", "# Ce<PERSON> setiap opsi direktori\n", "for data_dir, description in data_options:\n", "    if os.path.exists(data_dir):\n", "        print(f\"\\n📁 Ditemukan: {data_dir}\")\n", "        print(f\"   📝 {description}\")\n", "        \n", "        # Cek subdirectory dan hitung gambar\n", "        subdirs = []\n", "        total_images = 0\n", "        \n", "        for item in os.listdir(data_dir):\n", "            item_path = os.path.join(data_dir, item)\n", "            if os.path.isdir(item_path):\n", "                # Hitung gambar di kelas ini\n", "                image_files = [f for f in os.listdir(item_path) \n", "                             if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])\n", "                if image_files:  # <PERSON><PERSON> tambahkan jika ada gambar\n", "                    subdirs.append({\n", "                        'name': item,\n", "                        'count': len(image_files)\n", "                    })\n", "                    total_images += len(image_files)\n", "        \n", "        if subdirs:  # <PERSON><PERSON> ada kelas dengan gambar\n", "            print(f\"   📊 {len(subdirs)} kelas, {total_images} gambar total\")\n", "            print(f\"   📋 Kelas yang ditemukan:\")\n", "            for subdir in subdirs:\n", "                print(f\"      - {subdir['name']}: {subdir['count']} gambar\")\n", "            \n", "            # Gunakan direktori pertama yang valid\n", "            if not selected_data_dir:\n", "                selected_data_dir = data_dir\n", "                dataset_info = {\n", "                    'classes': subdirs,\n", "                    'total_images': total_images,\n", "                    'num_classes': len(subdirs)\n", "                }\n", "                print(f\"\\n✅ DIPILIH: {data_dir}\")\n", "        else:\n", "            print(f\"   ⚠️  Tidak ada kelas dengan gambar yang valid\")\n", "\n", "# <PERSON><PERSON> dete<PERSON>i\n", "if selected_data_dir:\n", "    main_data_dir = selected_data_dir\n", "    num_classes = dataset_info['num_classes']\n", "    \n", "    print(f\"\\n\" + \"=\"*60)\n", "    print(f\"🎯 KONFIGURASI DATA FINAL\")\n", "    print(f\"=\"*60)\n", "    print(f\"📁 Data directory: {main_data_dir}\")\n", "    print(f\"📊 Jumlah kelas: {num_classes}\")\n", "    print(f\"📊 Total gambar: {dataset_info['total_images']}\")\n", "    print(f\"🔧 Batch size: 32 (disesuaikan untuk environment lokal)\")\n", "    print(f\"📊 Validation split: 30%\")\n", "    \n", "    # Update num_classes variable untuk model\n", "    print(f\"\\n✅ Dataset siap digunakan!\")\n", "    \n", "else:\n", "    print(f\"\\n❌ TIDAK DITEMUKAN DATASET YANG VALID!\")\n", "    print(f\"\\n📋 Silakan pastikan salah satu direktori berikut ada dan berisi data:\")\n", "    for data_dir, description in data_options:\n", "        print(f\"  - {data_dir} ({description})\")\n", "    print(f\"\\n📁 Struktur direktori yang diharapkan:\")\n", "    print(f\"Dataset/ (atau ResizedDataset/)\")\n", "    print(f\"  ├── Lonchura leucogastroides/\")\n", "    print(f\"  ├── Lonchura maja/\")\n", "    print(f\"  ├── Lonchura punctulata/\")\n", "    print(f\"  └── Passer montanus/\")\n", "    print(f\"\\nSetiap subdirektori harus berisi gambar dari spesies burung yang sesuai.\")\n", "    \n", "    # Fallback\n", "    main_data_dir = './Dataset'  # Default fallback\n", "    num_classes = 4  # <PERSON><PERSON><PERSON> untuk 4 kelas burung\n", "    print(f\"\\n⚠️  Menggunakan fallback: {main_data_dir}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "FyG_sydDe2-v"}, "source": ["# Define data generators with additional data augmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:01:46.501550Z", "iopub.status.busy": "2024-06-13T09:01:46.501302Z", "iopub.status.idle": "2024-06-13T09:01:46.520550Z", "shell.execute_reply": "2024-06-13T09:01:46.519539Z", "shell.execute_reply.started": "2024-06-13T09:01:46.501529Z"}, "trusted": true, "id": "Dnr4HG1Ue2-w"}, "outputs": [], "source": ["# Data generator dengan augmentasi untuk training\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=20,\n", "    width_shift_range=0.2,\n", "    height_shift_range=0.2,\n", "    shear_range=0.2,\n", "    zoom_range=0.2,\n", "    horizontal_flip=True,\n", "    vertical_flip=True,\n", "    fill_mode='nearest',\n", "    validation_split=0.3,\n", ")\n", "\n", "# Data generator untuk validation (hanya rescaling)\n", "valid_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    validation_split=0.3\n", ")\n"]}, {"cell_type": "markdown", "metadata": {"id": "data-loading-section"}, "source": ["# Load Data dengan G<PERSON> yang Sudah Diproses ke 224x224"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:01:46.522220Z", "iopub.status.busy": "2024-06-13T09:01:46.521827Z", "iopub.status.idle": "2024-06-13T09:02:07.446026Z", "shell.execute_reply": "2024-06-13T09:02:07.445174Z", "shell.execute_reply.started": "2024-06-13T09:01:46.522183Z"}, "trusted": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "cbAPEYpke2-x", "executionInfo": {"status": "ok", "timestamp": 1753537601294, "user_tz": -420, "elapsed": 3608, "user": {"displayName": "404 Not found", "userId": "07680280710758519825"}}, "outputId": "112f0c87-aa63-482d-e42b-5249b98b7483"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n", "'Eurasian Tree Sparrow - Passer montanus'\n", "'Javan <PERSON> - Lonch<PERSON> leucoga<PERSON>'\n", "'Scaly-breasted Munia - Lonchura punctulata'\n", "'White-headed Munia - Lonchura maja'\n", "Found 558 images belonging to 4 classes.\n", "Found 236 images belonging to 4 classes.\n"]}], "source": ["# Input data - menggunakan data yang sudah diproses ke 224x224\n", "# Uncomment baris berikut jika menggunakan Google Colab\n", "# drive.mount('/content/drive')\n", "# main_data_dir = '/content/drive/MyDrive/images'\n", "# !ls '/content/drive/MyDrive/images'\n", "\n", "# Untuk environment lokal, main_data_dir sudah diset di cell sebelumnya\n", "print(f\"📁 Menggunakan data dari: {main_data_dir}\")\n", "\n", "# Cek apakah direktori data ada\n", "if os.path.exists(main_data_dir):\n", "    print(f\"✅ Data directory ditemukan!\")\n", "    \n", "    # Tampilkan info kelas dan jumlah gambar\n", "    print(\"\\n📊 Dataset info:\")\n", "    total_images = 0\n", "    for class_dir in os.listdir(main_data_dir):\n", "        class_path = os.path.join(main_data_dir, class_dir)\n", "        if os.path.isdir(class_path):\n", "            image_count = len([f for f in os.listdir(class_path) \n", "                              if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])\n", "            total_images += image_count\n", "            print(f\"  📂 {class_dir}: {image_count} images\")\n", "    \n", "    print(f\"\\n📈 Total images: {total_images}\")\n", "    \n", "    # Set batch size (disesuaikan untuk environment lokal)\n", "    batch_size = 32  # Reduced from 128 for local environment\n", "    print(f\"🔧 Batch size: {batch_size}\")\n", "    \n", "    # Define train and validation generators dengan data yang sudah diproses\n", "    print(\"\\n🔄 Creating data generators...\")\n", "    \n", "    train_generator = train_datagen.flow_from_directory(\n", "        main_data_dir,\n", "        target_size=(224, 224),  # G<PERSON>bar sudah 224x224, tapi tetap specify untuk konsistensi\n", "        batch_size=batch_size,\n", "        class_mode='categorical',\n", "        shuffle=True,\n", "        subset=\"training\"\n", "    )\n", "    \n", "    valid_generator = valid_datagen.flow_from_directory(\n", "        main_data_dir,\n", "        target_size=(224, 224),  # G<PERSON>bar sudah 224x224, tapi tetap specify untuk konsistensi\n", "        batch_size=batch_size,\n", "        class_mode='categorical',\n", "        shuffle=False,\n", "        subset=\"validation\"\n", "    )\n", "    \n", "    print(f\"\\n✅ Data generators created successfully!\")\n", "    print(f\"📊 Training samples: {train_generator.samples}\")\n", "    print(f\"📊 Validation samples: {valid_generator.samples}\")\n", "    print(f\"📊 Number of classes: {train_generator.num_classes}\")\n", "    print(f\"📊 Class indices: {train_generator.class_indices}\")\n", "    \n", "else:\n", "    print(f\"❌ Data directory tidak ditemukan: {main_data_dir}\")\n", "    print(\"Silakan pastikan data sudah diproses atau sesuaikan path data.\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "visualization-section"}, "source": ["# Visual<PERSON>si Sample Gambar yang Sudah Diproses"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualization-cell"}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> beberapa sample gambar dari setiap kelas\n", "def visualize_processed_samples(data_generator, num_samples=2):\n", "    \"\"\"\n", "    Visualisasi sample gambar dari data generator\n", "    \"\"\"\n", "    # Get class names\n", "    class_names = list(data_generator.class_indices.keys())\n", "    num_classes = len(class_names)\n", "    \n", "    # Create subplot\n", "    fig, axes = plt.subplots(num_classes, num_samples, figsize=(num_samples*4, num_classes*3))\n", "    if num_classes == 1:\n", "        axes = axes.reshape(1, -1)\n", "    if num_samples == 1:\n", "        axes = axes.reshape(-1, 1)\n", "    \n", "    # Get one batch of data\n", "    batch_images, batch_labels = next(data_generator)\n", "    \n", "    # Track samples per class\n", "    class_sample_count = {i: 0 for i in range(num_classes)}\n", "    \n", "    # Plot samples\n", "    for i, (image, label) in enumerate(zip(batch_images, batch_labels)):\n", "        class_idx = np.argmax(label)\n", "        \n", "        if class_sample_count[class_idx] < num_samples:\n", "            col = class_sample_count[class_idx]\n", "            \n", "            # Display image\n", "            axes[class_idx, col].imshow(image)\n", "            axes[class_idx, col].set_title(f'{class_names[class_idx]}\\nShape: {image.shape}')\n", "            axes[class_idx, col].axis('off')\n", "            \n", "            class_sample_count[class_idx] += 1\n", "        \n", "        # Break if we have enough samples for all classes\n", "        if all(count >= num_samples for count in class_sample_count.values()):\n", "            break\n", "    \n", "    # Fill empty subplots if needed\n", "    for class_idx in range(num_classes):\n", "        for col in range(class_sample_count[class_idx], num_samples):\n", "            axes[class_idx, col].text(0.5, 0.5, 'No sample\\navailable', \n", "                                    ha='center', va='center', transform=axes[class_idx, col].transAxes)\n", "            axes[class_idx, col].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Jalankan visualisasi jika data generator berhasil dibuat\n", "if 'train_generator' in locals():\n", "    print(\"🖼️  Menampilkan sample gambar yang sudah diproses ke 224x224:\")\n", "    visualize_processed_samples(train_generator, num_samples=3)\n", "    \n", "    # Reset generator set<PERSON>h visualisasi\n", "    train_generator.reset()\n", "else:\n", "    print(\"⚠️  Data generator belum dibuat. Silakan jalankan cell sebelumnya terlebih dahulu.\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "IHBZvtjre2-x"}, "source": ["# Load MobileNetV2 as base model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:02:07.448888Z", "iopub.status.busy": "2024-06-13T09:02:07.448568Z", "iopub.status.idle": "2024-06-13T09:02:10.377451Z", "shell.execute_reply": "2024-06-13T09:02:10.376419Z", "shell.execute_reply.started": "2024-06-13T09:02:07.448864Z"}, "trusted": true, "id": "Nv2IVETAe2-y"}, "outputs": [], "source": ["base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)"]}, {"cell_type": "markdown", "metadata": {"id": "bZZDMCC0e2-y"}, "source": ["# Freeze layers in base model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:02:10.379133Z", "iopub.status.busy": "2024-06-13T09:02:10.378843Z", "iopub.status.idle": "2024-06-13T09:02:10.387231Z", "shell.execute_reply": "2024-06-13T09:02:10.386225Z", "shell.execute_reply.started": "2024-06-13T09:02:10.379109Z"}, "trusted": true, "id": "RQ6kosZSe2-y"}, "outputs": [], "source": ["for layer in base_model.layers:\n", "    layer.trainable = False"]}, {"cell_type": "markdown", "metadata": {"id": "riqm0_Cre2-y"}, "source": ["# Add custom head"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:02:10.388703Z", "iopub.status.busy": "2024-06-13T09:02:10.388402Z", "iopub.status.idle": "2024-06-13T09:02:10.423174Z", "shell.execute_reply": "2024-06-13T09:02:10.422290Z", "shell.execute_reply.started": "2024-06-13T09:02:10.388678Z"}, "trusted": true, "id": "GkiL2Yzoe2-z"}, "outputs": [], "source": ["x = base_model.output\n", "x = GlobalAveragePooling2D()(x)\n", "x = Dropout(0.5)(x)\n", "x = Dense(1024, activation='relu')(x)\n", "x = Dropout(0.5)(x)\n", "predictions = Dense(num_classes, activation='softmax')(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:02:10.424548Z", "iopub.status.busy": "2024-06-13T09:02:10.424276Z", "iopub.status.idle": "2024-06-13T09:02:10.461203Z", "shell.execute_reply": "2024-06-13T09:02:10.460412Z", "shell.execute_reply.started": "2024-06-13T09:02:10.424526Z"}, "trusted": true, "id": "kXhFqgoKe2-z"}, "outputs": [], "source": ["# Combine base model and custom head\n", "model = Model(inputs=base_model.input, outputs=predictions)\n", "\n", "# Fine-tune the model by unfreezing some layers\n", "for layer in base_model.layers[:-10]:\n", "    layer.trainable = False\n", "\n", "# Implement learning rate scheduling\n", "lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(\n", "    initial_learning_rate=1e-4,\n", "    decay_steps=1000,\n", "    decay_rate=0.9\n", ")\n", "optimizer = Adam(learning_rate=lr_schedule)\n"]}, {"cell_type": "markdown", "metadata": {"id": "rkw4LMDFe2-z"}, "source": ["# Compile the model with class weights"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:02:10.462624Z", "iopub.status.busy": "2024-06-13T09:02:10.462309Z", "iopub.status.idle": "2024-06-13T09:02:10.476007Z", "shell.execute_reply": "2024-06-13T09:02:10.475191Z", "shell.execute_reply.started": "2024-06-13T09:02:10.462601Z"}, "trusted": true, "id": "vyGO3-hWe2-z"}, "outputs": [], "source": ["model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:02:10.699296Z", "iopub.status.busy": "2024-06-13T09:02:10.699044Z", "iopub.status.idle": "2024-06-13T09:43:06.998612Z", "shell.execute_reply": "2024-06-13T09:43:06.997810Z", "shell.execute_reply.started": "2024-06-13T09:02:10.699275Z"}, "trusted": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "PhpBCudce2-z", "executionInfo": {"status": "ok", "timestamp": 1753245239423, "user_tz": -420, "elapsed": 529707, "user": {"displayName": "404 Not found", "userId": "07680280710758519825"}}, "outputId": "6485975e-1b47-493a-9f0a-2253e11d7975"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/trainers/data_adapters/py_dataset_adapter.py:121: UserWarning: Your `PyDataset` class should call `super().__init__(**kwargs)` in its constructor. `**kwargs` can include `workers`, `use_multiprocessing`, `max_queue_size`. Do not pass these arguments to `fit()`, as they will be ignored.\n", "  self._warn_if_super_not_called()\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Epoch 1/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m73s\u001b[0m 13s/step - accuracy: 0.4502 - loss: 1.7141 - val_accuracy: 0.5636 - val_loss: 1.1225\n", "Epoch 2/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 8s/step - accuracy: 0.4326 - loss: 1.5610 - val_accuracy: 0.5847 - val_loss: 1.0440\n", "Epoch 3/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 8s/step - accuracy: 0.4888 - loss: 1.4154 - val_accuracy: 0.6102 - val_loss: 0.9855\n", "Epoch 4/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 8s/step - accuracy: 0.5320 - loss: 1.2959 - val_accuracy: 0.6017 - val_loss: 0.9687\n", "Epoch 5/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 8s/step - accuracy: 0.5379 - loss: 1.2113 - val_accuracy: 0.6525 - val_loss: 0.9480\n", "Epoch 6/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 8s/step - accuracy: 0.5697 - loss: 1.1370 - val_accuracy: 0.6695 - val_loss: 0.9274\n", "Epoch 7/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 8s/step - accuracy: 0.5636 - loss: 1.1659 - val_accuracy: 0.6229 - val_loss: 0.9443\n", "Epoch 8/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m35s\u001b[0m 7s/step - accuracy: 0.6183 - loss: 1.0758 - val_accuracy: 0.6737 - val_loss: 0.9103\n", "Epoch 9/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 8s/step - accuracy: 0.5852 - loss: 1.0482 - val_accuracy: 0.6864 - val_loss: 0.8964\n", "Epoch 10/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m35s\u001b[0m 7s/step - accuracy: 0.5810 - loss: 1.0406 - val_accuracy: 0.6949 - val_loss: 0.8450\n", "Epoch 11/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m35s\u001b[0m 7s/step - accuracy: 0.6177 - loss: 0.9975 - val_accuracy: 0.6822 - val_loss: 0.8595\n", "Epoch 12/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 8s/step - accuracy: 0.5939 - loss: 1.0077 - val_accuracy: 0.6864 - val_loss: 0.8487\n", "Epoch 13/30\n", "\u001b[1m5/5\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m35s\u001b[0m 7s/step - accuracy: 0.6296 - loss: 0.9746 - val_accuracy: 0.7076 - val_loss: 0.8634\n"]}], "source": ["# Implement early stopping callback\n", "early_stopping = tf.keras.callbacks.EarlyStopping(\n", "    monitor='val_loss',\n", "    patience=3,\n", "    restore_best_weights=True\n", ")\n", "\n", "# Train the model with modified settings\n", "history = model.fit(\n", "    train_generator,\n", "    epochs=30,  # Increase epochs for better training\n", "    validation_data=valid_generator,\n", "    callbacks=[early_stopping],\n", "    verbose=1\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "5rRRmwOhe2-z"}, "source": ["# Evaluate the model on test data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:43:07.000037Z", "iopub.status.busy": "2024-06-13T09:43:06.999726Z", "iopub.status.idle": "2024-06-13T09:43:35.067231Z", "shell.execute_reply": "2024-06-13T09:43:35.066421Z", "shell.execute_reply.started": "2024-06-13T09:43:06.999995Z"}, "trusted": true, "id": "gph4eKTFe2-z"}, "outputs": [], "source": ["test_loss, test_accuracy = model.evaluate(valid_generator)\n", "print(\"Test Loss:\", test_loss)\n", "print(\"Test Accuracy:\", test_accuracy)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:43:35.068483Z", "iopub.status.busy": "2024-06-13T09:43:35.068224Z", "iopub.status.idle": "2024-06-13T09:43:43.664874Z", "shell.execute_reply": "2024-06-13T09:43:43.663928Z", "shell.execute_reply.started": "2024-06-13T09:43:35.068454Z"}, "trusted": true, "id": "K1edofBSe2-z"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from sklearn.metrics import confusion_matrix\n", "# Define the path to the validation directory\n", "validation_dir = '/content/drive/MyDrive/data_validation'\n", "\n", "# Define the data generators\n", "validation_datagen = ImageDataGenerator(rescale=1./255)\n", "\n", "# Load the validation data using the ImageDataGenerator\n", "validation_generator = validation_datagen.flow_from_directory(\n", "    validation_dir,\n", "    target_size=(224, 224),\n", "    batch_size=batch_size,\n", "    class_mode='categorical',\n", "    shuffle=False\n", ")\n", "\n", "# Get the number of classes\n", "num_classes = len(validation_generator.class_indices)\n", "\n", "# Define the class labels\n", "class_labels = list(validation_generator.class_indices.keys())\n", "\n", "# Make predictions on the validation data\n", "validation_predictions = model.predict(validation_generator)\n", "predicted_labels_validation = np.argmax(validation_predictions, axis=1)\n", "\n", "# Get the true labels for the validation data\n", "true_labels_validation = validation_generator.classes\n", "\n", "# Calculate confusion matrix for validation data\n", "conf_matrix_validation = confusion_matrix(true_labels_validation, predicted_labels_validation)\n", "\n", "# Plot the confusion matrix\n", "plt.figure(figsize=(15, 10))\n", "sns.heatmap(conf_matrix_validation, annot=True, fmt='d', xticklabels=class_labels, yticklabels=class_labels, cmap='Reds')\n", "plt.xlabel('Predicted Label')\n", "plt.ylabel('True Label')\n", "plt.title('Confusion Matrix')\n", "plt.xticks(rotation=90)\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {"id": "INTrSt5Ce2-0"}, "source": ["# Plot confusion matrix"]}, {"cell_type": "markdown", "metadata": {"id": "359FbL7te2-0"}, "source": ["# Save the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:57:18.592758Z", "iopub.status.busy": "2024-06-13T09:57:18.591925Z", "iopub.status.idle": "2024-06-13T09:57:19.101822Z", "shell.execute_reply": "2024-06-13T09:57:19.101036Z", "shell.execute_reply.started": "2024-06-13T09:57:18.592726Z"}, "trusted": true, "id": "xu-XHm-ge2-0"}, "outputs": [], "source": ["model.save('birds_classification.h5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-06-13T09:57:22.346263Z", "iopub.status.busy": "2024-06-13T09:57:22.345905Z", "iopub.status.idle": "2024-06-13T09:57:22.352990Z", "shell.execute_reply": "2024-06-13T09:57:22.352027Z", "shell.execute_reply.started": "2024-06-13T09:57:22.346236Z"}, "trusted": true, "id": "KbDCfhAre2-0"}, "outputs": [], "source": ["from IPython.display import FileLink\n", "FileLink(r'birds_classification.h5')"]}], "metadata": {"kaggle": {"accelerator": "gpu", "dataSources": [{"datasetId": 5200219, "sourceId": 8675724, "sourceType": "datasetVersion"}, {"datasetId": 5204083, "sourceId": 8680931, "sourceType": "datasetVersion"}], "dockerImageVersionId": 30732, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}, "colab": {"provenance": [], "gpuType": "T4"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}