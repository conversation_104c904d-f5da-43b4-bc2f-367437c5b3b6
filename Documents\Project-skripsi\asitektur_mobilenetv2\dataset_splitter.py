#!/usr/bin/env python3
"""
Dataset Splitter - Pembagi Dataset 70% Training dan 30% Validation
Script untuk membagi dataset secara adil berdasarkan nama file dengan proporsi 70:30
Mendukung pembagian berdasarkan nama file yang sudah terurut untuk konsistensi
"""

import os
import shutil
from pathlib import Path
import random
import math
from collections import defaultdict

def create_train_val_split(source_dir, output_dir, train_ratio=0.7, random_seed=42, preview=False):
    """
    Membagi dataset menjadi training dan validation dengan proporsi yang ditentukan
    
    Args:
        source_dir (str): Path ke direktori sumber (ResizedDataset/Dataset)
        output_dir (str): Path ke direktori output
        train_ratio (float): Proporsi untuk training (default: 0.7 = 70%)
        random_seed (int): Seed untuk random generator (untuk konsistensi)
        preview (bool): Jika True, hanya menampilkan preview tanpa copy file
    
    Returns:
        dict: Summary hasil pembagian
    """
    
    source_path = Path(source_dir)
    output_path = Path(output_dir)
    
    if not source_path.exists():
        print(f"❌ Error: Source directory '{source_dir}' tidak ditemukan!")
        return None
    
    # Set random seed untuk konsistensi
    random.seed(random_seed)
    
    # Dapatkan semua subfolder (kelas)
    class_folders = [f for f in source_path.iterdir() if f.is_dir()]
    
    if not class_folders:
        print(f"❌ Error: Tidak ada subfolder dalam '{source_dir}'!")
        return None
    
    # Urutkan folder untuk konsistensi
    class_folders.sort(key=lambda x: x.name.lower())
    
    print(f"🔄 DATASET SPLITTER")
    print("=" * 60)
    print(f"📁 Source: {source_path}")
    print(f"📁 Output: {output_path}")
    print(f"📊 Train ratio: {train_ratio*100:.0f}%")
    print(f"📊 Validation ratio: {(1-train_ratio)*100:.0f}%")
    print(f"🎲 Random seed: {random_seed}")
    
    if preview:
        print(f"🔍 MODE: PREVIEW")
    else:
        print(f"🔄 MODE: COPY FILES")
    
    print(f"\n📋 Ditemukan {len(class_folders)} kelas:")
    
    # Analisis setiap kelas
    class_stats = {}
    total_files = 0
    
    for class_folder in class_folders:
        # Dapatkan semua file gambar
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']:
            image_files.extend(class_folder.glob(ext))
            image_files.extend(class_folder.glob(ext.upper()))
        
        # Urutkan berdasarkan nama untuk konsistensi
        image_files.sort(key=lambda x: x.name.lower())
        
        total_count = len(image_files)
        train_count = math.floor(total_count * train_ratio)
        val_count = total_count - train_count
        
        class_stats[class_folder.name] = {
            'total': total_count,
            'train': train_count,
            'val': val_count,
            'files': image_files
        }
        
        total_files += total_count
        
        print(f"  📁 {class_folder.name}: {total_count} files → Train: {train_count}, Val: {val_count}")
    
    print(f"\n📊 Total keseluruhan: {total_files} files")
    
    # Hitung total train dan val
    total_train = sum(stats['train'] for stats in class_stats.values())
    total_val = sum(stats['val'] for stats in class_stats.values())
    
    print(f"📊 Total Train: {total_train} files ({total_train/total_files*100:.1f}%)")
    print(f"📊 Total Validation: {total_val} files ({total_val/total_files*100:.1f}%)")
    
    if not preview:
        # Konfirmasi
        confirm = input(f"\n❓ Lanjutkan pembagian dataset? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'ya']:
            print("❌ Pembagian dataset dibatalkan.")
            return None
        
        # Buat direktori output
        train_dir = output_path / "train"
        val_dir = output_path / "validation"
        
        train_dir.mkdir(parents=True, exist_ok=True)
        val_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n{'🔍 Preview pembagian:' if preview else '🔄 Memulai pembagian dataset:'}")
    print("=" * 60)
    
    # Proses setiap kelas
    copy_stats = defaultdict(int)
    
    for class_name, stats in class_stats.items():
        print(f"\n📁 Memproses kelas: {class_name}")
        
        files = stats['files'].copy()
        
        # Shuffle files untuk randomisasi yang konsisten
        random.shuffle(files)
        
        # Bagi files
        train_files = files[:stats['train']]
        val_files = files[stats['train']:]
        
        if not preview:
            # Buat direktori kelas
            train_class_dir = train_dir / class_name
            val_class_dir = val_dir / class_name
            
            train_class_dir.mkdir(exist_ok=True)
            val_class_dir.mkdir(exist_ok=True)
        
        # Copy training files
        print(f"  📚 Training files: {len(train_files)}")
        for i, file_path in enumerate(train_files, 1):
            if preview:
                if i <= 3:  # Tampilkan 3 file pertama sebagai contoh
                    print(f"    → train/{class_name}/{file_path.name}")
                elif i == 4 and len(train_files) > 3:
                    print(f"    → ... dan {len(train_files)-3} file lainnya")
            else:
                dest_path = train_class_dir / file_path.name
                shutil.copy2(file_path, dest_path)
                copy_stats['train_copied'] += 1
        
        # Copy validation files
        print(f"  📝 Validation files: {len(val_files)}")
        for i, file_path in enumerate(val_files, 1):
            if preview:
                if i <= 3:  # Tampilkan 3 file pertama sebagai contoh
                    print(f"    → validation/{class_name}/{file_path.name}")
                elif i == 4 and len(val_files) > 3:
                    print(f"    → ... dan {len(val_files)-3} file lainnya")
            else:
                dest_path = val_class_dir / file_path.name
                shutil.copy2(file_path, dest_path)
                copy_stats['val_copied'] += 1
    
    # Summary
    print("\n" + "=" * 60)
    if preview:
        print(f"🔍 PREVIEW SELESAI!")
        print(f"   📊 Total yang akan di-copy ke train: {total_train} files")
        print(f"   📊 Total yang akan di-copy ke validation: {total_val} files")
        print(f"   📁 Total kelas: {len(class_stats)}")
    else:
        print(f"🎉 PEMBAGIAN DATASET SELESAI!")
        print(f"   ✅ Train files copied: {copy_stats['train_copied']}")
        print(f"   ✅ Validation files copied: {copy_stats['val_copied']}")
        print(f"   📁 Output directory: {output_path}")
        print(f"   📁 Train directory: {train_dir}")
        print(f"   📁 Validation directory: {val_dir}")
    
    return {
        'total_files': total_files,
        'total_train': total_train,
        'total_val': total_val,
        'classes': len(class_stats),
        'class_stats': class_stats,
        'output_dir': str(output_path) if not preview else None
    }

def main():
    print("🔄 DATASET SPLITTER - 70% Training, 30% Validation")
    print("=" * 60)
    print("Script ini akan membagi dataset dengan proporsi 70:30")
    print("Pembagian dilakukan secara adil untuk setiap kelas berdasarkan nama file")
    print()
    
    # Default paths
    default_source = "ResizedDataset/Dataset"
    default_output = "SplitDataset"
    
    # Input source directory
    source_dir = input(f"📁 Source directory (default: {default_source}): ").strip()
    if not source_dir:
        source_dir = default_source
    
    # Input output directory
    output_dir = input(f"📁 Output directory (default: {default_output}): ").strip()
    if not output_dir:
        output_dir = default_output
    
    # Input train ratio
    train_ratio_input = input("📊 Train ratio (default: 0.7 = 70%): ").strip()
    try:
        train_ratio = float(train_ratio_input) if train_ratio_input else 0.7
        if not 0.1 <= train_ratio <= 0.9:
            print("⚠️  Train ratio harus antara 0.1 dan 0.9, menggunakan default 0.7")
            train_ratio = 0.7
    except ValueError:
        print("⚠️  Input tidak valid, menggunakan default 0.7")
        train_ratio = 0.7
    
    # Input random seed
    seed_input = input("🎲 Random seed (default: 42): ").strip()
    try:
        random_seed = int(seed_input) if seed_input else 42
    except ValueError:
        print("⚠️  Seed tidak valid, menggunakan default 42")
        random_seed = 42
    
    # Preview dulu
    print("\n" + "="*60)
    result = create_train_val_split(source_dir, output_dir, train_ratio, random_seed, preview=True)
    
    if result is None:
        return
    
    # Konfirmasi untuk copy sesungguhnya
    print("\n" + "="*60)
    confirm = input("❓ Lanjutkan dengan copy file sesungguhnya? (y/n): ").strip().lower()
    if confirm in ['y', 'yes', 'ya']:
        create_train_val_split(source_dir, output_dir, train_ratio, random_seed, preview=False)
    else:
        print("❌ Pembagian dataset dibatalkan.")

def analyze_dataset(dataset_dir):
    """
    Menganalisis dataset yang sudah dibagi
    """
    dataset_path = Path(dataset_dir)

    if not dataset_path.exists():
        print(f"❌ Dataset directory '{dataset_dir}' tidak ditemukan!")
        return

    train_dir = dataset_path / "train"
    val_dir = dataset_path / "validation"

    if not train_dir.exists() or not val_dir.exists():
        print(f"❌ Train atau validation directory tidak ditemukan!")
        return

    print(f"📊 ANALISIS DATASET: {dataset_dir}")
    print("=" * 50)

    # Analisis train
    train_classes = [f for f in train_dir.iterdir() if f.is_dir()]
    train_stats = {}
    total_train = 0

    for class_dir in sorted(train_classes, key=lambda x: x.name):
        files = list(class_dir.glob("*"))
        count = len([f for f in files if f.is_file()])
        train_stats[class_dir.name] = count
        total_train += count

    # Analisis validation
    val_classes = [f for f in val_dir.iterdir() if f.is_dir()]
    val_stats = {}
    total_val = 0

    for class_dir in sorted(val_classes, key=lambda x: x.name):
        files = list(class_dir.glob("*"))
        count = len([f for f in files if f.is_file()])
        val_stats[class_dir.name] = count
        total_val += count

    # Tampilkan hasil
    print(f"📚 TRAINING SET: {total_train} files")
    print(f"📝 VALIDATION SET: {total_val} files")
    print(f"📊 Total: {total_train + total_val} files")
    print(f"📊 Ratio: {total_train/(total_train+total_val)*100:.1f}% train, {total_val/(total_train+total_val)*100:.1f}% val")
    print()

    print("📋 Detail per kelas:")
    all_classes = set(train_stats.keys()) | set(val_stats.keys())

    for class_name in sorted(all_classes):
        train_count = train_stats.get(class_name, 0)
        val_count = val_stats.get(class_name, 0)
        total_class = train_count + val_count

        if total_class > 0:
            train_pct = train_count / total_class * 100
            val_pct = val_count / total_class * 100
            print(f"  📁 {class_name}: {total_class} total → Train: {train_count} ({train_pct:.1f}%), Val: {val_count} ({val_pct:.1f}%)")

def quick_split():
    """
    Quick split dengan parameter default
    """
    print("⚡ QUICK SPLIT - 70:30 dengan parameter default")
    print("=" * 50)

    source_dir = "ResizedDataset/Dataset"
    output_dir = "SplitDataset"

    if not Path(source_dir).exists():
        print(f"❌ Source directory '{source_dir}' tidak ditemukan!")
        return

    # Preview
    result = create_train_val_split(source_dir, output_dir, 0.7, 42, preview=True)

    if result:
        confirm = input(f"\n❓ Lanjutkan quick split? (y/n): ").strip().lower()
        if confirm in ['y', 'yes', 'ya']:
            create_train_val_split(source_dir, output_dir, 0.7, 42, preview=False)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "analyze":
            dataset_dir = sys.argv[2] if len(sys.argv) > 2 else "SplitDataset"
            analyze_dataset(dataset_dir)
        elif sys.argv[1] == "quick":
            quick_split()
        else:
            print("Usage: python dataset_splitter.py [analyze|quick] [dataset_dir]")
    else:
        main()
