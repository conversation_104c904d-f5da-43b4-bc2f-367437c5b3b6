#!/usr/bin/env python3
"""
Script untuk mengecek dan menampilkan detail gambar yang gagal diproses
"""

import os
import cv2
import numpy as np
from PIL import Image

def check_image_details(image_path):
    """
    Cek detail gambar dan kemungkinan masalah
    """
    details = {
        'filename': os.path.basename(image_path),
        'path': image_path,
        'exists': False,
        'readable_cv2': False,
        'readable_pil': False,
        'size': None,
        'format': None,
        'file_size': 0,
        'issues': []
    }
    
    # Cek apakah file ada
    if not os.path.exists(image_path):
        details['issues'].append("File tidak ditemukan")
        return details
    
    details['exists'] = True
    details['file_size'] = os.path.getsize(image_path)
    
    # Cek ukuran file
    if details['file_size'] == 0:
        details['issues'].append("File kosong (0 bytes)")
        return details
    
    if details['file_size'] < 100:  # File terlalu kecil
        details['issues'].append(f"File terlalu kecil ({details['file_size']} bytes)")
    
    # Cek dengan OpenCV
    try:
        image_cv2 = cv2.imread(image_path)
        if image_cv2 is not None:
            details['readable_cv2'] = True
            details['size'] = f"{image_cv2.shape[1]}x{image_cv2.shape[0]}"
            
            # Cek ukuran gambar
            if image_cv2.shape[0] <= 0 or image_cv2.shape[1] <= 0:
                details['issues'].append("Ukuran gambar tidak valid")
        else:
            details['issues'].append("Tidak bisa dibaca dengan OpenCV")
    except Exception as e:
        details['issues'].append(f"Error OpenCV: {str(e)}")
    
    # Cek dengan PIL
    try:
        with Image.open(image_path) as img:
            details['readable_pil'] = True
            details['format'] = img.format
            if not details['size']:  # Jika OpenCV gagal
                details['size'] = f"{img.width}x{img.height}"
            
            # Cek mode gambar
            if img.mode not in ['RGB', 'RGBA', 'L']:
                details['issues'].append(f"Mode gambar tidak didukung: {img.mode}")
                
    except Exception as e:
        details['issues'].append(f"Error PIL: {str(e)}")
    
    # Cek ekstensi file
    ext = os.path.splitext(image_path)[1].lower()
    if ext not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
        details['issues'].append(f"Ekstensi tidak didukung: {ext}")
    
    return details

def scan_directory_for_issues(input_dir):
    """
    Scan direktori untuk mencari gambar yang bermasalah
    """
    if not os.path.exists(input_dir):
        print(f"❌ Direktori tidak ditemukan: {input_dir}")
        return
    
    print(f"🔍 Scanning directory: {input_dir}")
    print("=" * 60)
    
    total_files = 0
    problematic_files = []
    good_files = []
    
    # Scan setiap subdirektori
    for class_name in os.listdir(input_dir):
        class_path = os.path.join(input_dir, class_name)
        
        if not os.path.isdir(class_path):
            continue
        
        print(f"\n📂 Checking class: {class_name}")
        print("-" * 40)
        
        class_issues = 0
        class_good = 0
        
        # Cek setiap file
        for filename in os.listdir(class_path):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')):
                file_path = os.path.join(class_path, filename)
                total_files += 1
                
                details = check_image_details(file_path)
                
                if details['issues']:
                    class_issues += 1
                    problematic_files.append(details)
                    
                    print(f"  ❌ {filename}")
                    print(f"     Size: {details['size'] or 'Unknown'}")
                    print(f"     Format: {details['format'] or 'Unknown'}")
                    print(f"     File size: {details['file_size']} bytes")
                    print(f"     OpenCV: {'✅' if details['readable_cv2'] else '❌'}")
                    print(f"     PIL: {'✅' if details['readable_pil'] else '❌'}")
                    print(f"     Issues: {', '.join(details['issues'])}")
                    print()
                else:
                    class_good += 1
                    good_files.append(details)
        
        print(f"📊 {class_name} Summary: ✅ {class_good} good, ❌ {class_issues} issues")
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 SCAN RESULTS")
    print(f"{'='*60}")
    print(f"📈 Total files scanned: {total_files}")
    print(f"✅ Good files: {len(good_files)}")
    print(f"❌ Problematic files: {len(problematic_files)}")
    print(f"📊 Success rate: {(len(good_files)/total_files*100):.1f}%" if total_files > 0 else "0%")
    
    # Detail problematic files
    if problematic_files:
        print(f"\n❌ DETAILED PROBLEMATIC FILES:")
        print("-" * 60)
        
        for i, details in enumerate(problematic_files, 1):
            print(f"{i:3d}. {details['path']}")
            print(f"     Issues: {', '.join(details['issues'])}")
            print(f"     Size: {details['size'] or 'Unknown'}")
            print(f"     File size: {details['file_size']} bytes")
            print()
    
    # Recommendations
    if problematic_files:
        print("💡 RECOMMENDATIONS:")
        print("-" * 60)
        
        # Group by issue type
        issue_counts = {}
        for details in problematic_files:
            for issue in details['issues']:
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"• {issue}: {count} files")
            
            if "tidak bisa dibaca" in issue.lower():
                print("  → Coba convert ulang atau hapus file yang corrupt")
            elif "ukuran" in issue.lower():
                print("  → Cek apakah file benar-benar gambar")
            elif "ekstensi" in issue.lower():
                print("  → Rename file dengan ekstensi yang benar")
            elif "mode" in issue.lower():
                print("  → Convert ke RGB mode")
        
        print(f"\n🔧 Untuk memperbaiki:")
        print("1. Hapus atau perbaiki file yang bermasalah")
        print("2. Jalankan ulang script konversi")
        print("3. Atau skip file bermasalah dengan menambah error handling")
    
    return problematic_files

def main():
    print("🔍 Image Issue Checker")
    print("=" * 50)
    
    # Input directory
    input_dir = input("📁 Masukkan path direktori untuk dicek (atau Enter untuk './data'): ").strip()
    if not input_dir:
        input_dir = "./data"
    
    # Scan directory
    problematic_files = scan_directory_for_issues(input_dir)
    
    # Save report
    if problematic_files:
        report_file = "problematic_images_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("PROBLEMATIC IMAGES REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            for i, details in enumerate(problematic_files, 1):
                f.write(f"{i}. {details['path']}\n")
                f.write(f"   Issues: {', '.join(details['issues'])}\n")
                f.write(f"   Size: {details['size'] or 'Unknown'}\n")
                f.write(f"   File size: {details['file_size']} bytes\n")
                f.write(f"   OpenCV readable: {details['readable_cv2']}\n")
                f.write(f"   PIL readable: {details['readable_pil']}\n")
                f.write("\n")
        
        print(f"\n💾 Report saved to: {report_file}")

if __name__ == "__main__":
    main()
