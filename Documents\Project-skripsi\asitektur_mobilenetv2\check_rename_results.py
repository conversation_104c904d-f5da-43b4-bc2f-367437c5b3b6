#!/usr/bin/env python3
"""
Check Rename Results
Script untuk memeriksa hasil rename dan menampilkan summary
"""

import os
from pathlib import Path
from collections import defaultdict

def check_rename_results(base_directory):
    """
    Memeriksa hasil rename dalam semua subfolder
    
    Args:
        base_directory (str): Directory yang berisi folder-folder
    """
    
    base_path = Path(base_directory)
    
    if not base_path.exists():
        print(f"❌ Directory '{base_directory}' tidak ditemukan!")
        return
    
    # Dapatkan semua subfolder
    subfolders = [f for f in base_path.iterdir() if f.is_dir()]
    
    if not subfolders:
        print(f"📁 Tidak ada subfolder dalam '{base_directory}'!")
        return
    
    # Urutkan folder
    subfolders.sort(key=lambda x: x.name.lower())
    
    print(f"📊 HASIL RENAME CHECKER")
    print("=" * 60)
    print(f"📁 Base directory: {base_path}")
    print(f"📊 Total folder: {len(subfolders)}")
    print()
    
    total_files = 0
    total_renamed = 0
    total_original = 0
    
    results = []
    
    for folder in subfolders:
        folder_name = folder.name
        files = [f for f in folder.iterdir() if f.is_file()]
        
        if not files:
            results.append({
                'folder': folder_name,
                'total': 0,
                'renamed': 0,
                'original': 0,
                'status': 'KOSONG'
            })
            continue
        
        # Hitung file yang sudah direname vs yang belum
        renamed_files = 0
        original_files = 0
        
        for file_path in files:
            filename = file_path.name
            # Cek apakah file sudah mengikuti format "FolderName Number.ext"
            if filename.startswith(f"{folder_name} ") and filename.replace(f"{folder_name} ", "").split('.')[0].isdigit():
                renamed_files += 1
            else:
                original_files += 1
        
        total_files += len(files)
        total_renamed += renamed_files
        total_original += original_files
        
        # Tentukan status
        if original_files == 0:
            status = "✅ SEMUA RENAMED"
        elif renamed_files == 0:
            status = "❌ BELUM RENAMED"
        else:
            status = "⚠️ SEBAGIAN RENAMED"
        
        results.append({
            'folder': folder_name,
            'total': len(files),
            'renamed': renamed_files,
            'original': original_files,
            'status': status
        })
    
    # Tampilkan hasil
    print(f"📋 DETAIL HASIL PER FOLDER:")
    print("-" * 60)
    print(f"{'FOLDER':<25} {'TOTAL':<8} {'RENAMED':<8} {'ORIGINAL':<8} {'STATUS'}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['folder']:<25} {result['total']:<8} {result['renamed']:<8} {result['original']:<8} {result['status']}")
    
    print("-" * 60)
    print(f"📊 SUMMARY KESELURUHAN:")
    print(f"   📁 Total folder: {len(subfolders)}")
    print(f"   📄 Total file: {total_files}")
    print(f"   ✅ File renamed: {total_renamed}")
    print(f"   ❌ File original: {total_original}")
    
    if total_original == 0:
        print(f"   🎉 STATUS: SEMUA FILE BERHASIL DIRENAME!")
    else:
        percentage = (total_renamed / total_files) * 100 if total_files > 0 else 0
        print(f"   📊 Persentase renamed: {percentage:.1f}%")
    
    # Tampilkan contoh file dari beberapa folder
    print(f"\n📋 CONTOH FILE HASIL RENAME:")
    print("-" * 60)
    
    for result in results[:5]:  # Tampilkan 5 folder pertama
        folder_name = result['folder']
        folder_path = base_path / folder_name
        files = [f for f in folder_path.iterdir() if f.is_file()]
        
        if files:
            files.sort(key=lambda x: x.name.lower())
            print(f"📁 {folder_name}:")
            
            # Tampilkan 3 file pertama
            for file_path in files[:3]:
                print(f"   → {file_path.name}")
            
            if len(files) > 3:
                print(f"   → ... dan {len(files)-3} file lainnya")
            print()

def check_specific_folder(folder_path):
    """Cek hasil rename untuk folder tertentu"""
    
    folder = Path(folder_path)
    
    if not folder.exists():
        print(f"❌ Folder '{folder_path}' tidak ditemukan!")
        return
    
    folder_name = folder.name
    files = [f for f in folder.iterdir() if f.is_file()]
    
    if not files:
        print(f"📁 Folder '{folder_name}' kosong!")
        return
    
    print(f"📊 HASIL RENAME - {folder_name}")
    print("=" * 50)
    print(f"📁 Folder: {folder}")
    print(f"📊 Total file: {len(files)}")
    
    # Analisis file
    renamed_files = []
    original_files = []
    
    for file_path in files:
        filename = file_path.name
        if filename.startswith(f"{folder_name} ") and filename.replace(f"{folder_name} ", "").split('.')[0].isdigit():
            renamed_files.append(filename)
        else:
            original_files.append(filename)
    
    print(f"✅ File renamed: {len(renamed_files)}")
    print(f"❌ File original: {len(original_files)}")
    
    if original_files:
        print(f"\n⚠️ File yang belum direname:")
        for filename in original_files[:10]:  # Tampilkan 10 pertama
            print(f"   - {filename}")
        if len(original_files) > 10:
            print(f"   ... dan {len(original_files)-10} file lainnya")
    
    if renamed_files:
        print(f"\n✅ Contoh file yang sudah direname:")
        for filename in renamed_files[:10]:  # Tampilkan 10 pertama
            print(f"   - {filename}")
        if len(renamed_files) > 10:
            print(f"   ... dan {len(renamed_files)-10} file lainnya")

def main():
    print("📊 RENAME RESULTS CHECKER")
    print("=" * 50)
    
    # Contoh direktori yang umum
    common_dirs = [
        "ResizedDatasetUnknown",
        "ResizedDataset", 
        "Dataset",
        "raw_data"
    ]
    
    print("Pilih directory untuk dicek:")
    for i, dir_path in enumerate(common_dirs, 1):
        if Path(dir_path).exists():
            subfolder_count = len([f for f in Path(dir_path).iterdir() if f.is_dir()])
            print(f"  {i}. {dir_path} ({subfolder_count} folders)")
        else:
            print(f"  {i}. {dir_path} (tidak ditemukan)")
    
    print(f"  {len(common_dirs)+1}. Input manual (multiple folders)")
    print(f"  {len(common_dirs)+2}. Single folder")
    
    try:
        choice = int(input(f"\nPilih (1-{len(common_dirs)+2}): "))
        
        if 1 <= choice <= len(common_dirs):
            selected_dir = common_dirs[choice-1]
            check_rename_results(selected_dir)
            
        elif choice == len(common_dirs)+1:
            dir_path = input("📁 Masukkan path directory: ").strip().strip('"')
            check_rename_results(dir_path)
            
        elif choice == len(common_dirs)+2:
            folder_path = input("📁 Masukkan path folder: ").strip().strip('"')
            check_specific_folder(folder_path)
        else:
            print("❌ Pilihan tidak valid!")
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ Program dibatalkan.")

if __name__ == "__main__":
    main()
