#!/usr/bin/env python3
"""
MobileNetV2 Bird Classification dengan Image Preprocessing
Script ini melakukan preprocessing gambar ke 224x224 sebelum training
"""

import tensorflow as tf
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import os
import cv2
from PIL import Image
from pathlib import Path

def preprocess_images_for_training(input_dir, output_dir, target_size=(224, 224), method='padding'):
    """
    Preprocess gambar untuk training MobileNetV2
    
    Args:
        input_dir: direktori input dengan struktur kelas
        output_dir: direktori output untuk gambar yang sudah diproses
        target_size: ukuran target (224, 224)
        method: 'padding' atau 'stretch'
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directory
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Supported image extensions
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    total_processed = 0
    class_counts = {}
    
    print(f"🔄 Memulai preprocessing gambar...")
    print(f"📁 Input: {input_dir}")
    print(f"📁 Output: {output_dir}")
    print(f"🎯 Target size: {target_size}")
    print(f"⚙️  Method: {method}")
    print("-" * 50)
    
    # Process each class directory
    for class_dir in input_path.iterdir():
        if class_dir.is_dir():
            print(f"📂 Processing class: {class_dir.name}")
            
            # Create output class directory
            output_class_dir = output_path / class_dir.name
            output_class_dir.mkdir(exist_ok=True)
            
            # Get all image files
            image_files = [f for f in class_dir.iterdir() 
                          if f.suffix.lower() in image_extensions]
            
            class_count = 0
            
            for i, image_file in enumerate(image_files):
                try:
                    # Load image
                    image = cv2.imread(str(image_file))
                    if image is None:
                        continue
                    
                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    original_shape = image.shape
                    
                    # Apply preprocessing
                    if method == 'padding':
                        processed_image = resize_with_padding(image, target_size)
                    else:
                        processed_image = resize_with_stretch(image, target_size)
                    
                    # Save processed image
                    output_file = output_class_dir / f"{image_file.stem}.jpg"
                    
                    # Convert to PIL and save
                    pil_image = Image.fromarray(processed_image)
                    pil_image.save(str(output_file), 'JPEG', quality=95)
                    
                    total_processed += 1
                    class_count += 1
                    
                except Exception as e:
                    print(f"❌ Error processing {image_file}: {e}")
            
            class_counts[class_dir.name] = class_count
            print(f"✅ {class_dir.name}: {class_count} images processed")
    
    print("-" * 50)
    print(f"🎉 Preprocessing selesai!")
    print(f"📊 Total images processed: {total_processed}")
    print("📈 Class distribution:")
    for class_name, count in class_counts.items():
        print(f"   - {class_name}: {count} images")
    
    return class_counts

def resize_with_padding(image, target_size=(224, 224), pad_color=(0, 0, 0)):
    """Resize dengan padding untuk mempertahankan aspect ratio"""
    if isinstance(image, np.ndarray):
        image = Image.fromarray(image)
    
    original_width, original_height = image.size
    target_width, target_height = target_size
    
    # Calculate scaling factor
    scale = min(target_width / original_width, target_height / original_height)
    
    # Calculate new dimensions
    new_width = int(original_width * scale)
    new_height = int(original_height * scale)
    
    # Resize image
    resized_image = image.resize((new_width, new_height), Image.LANCZOS)
    
    # Create padded image
    new_image = Image.new('RGB', target_size, pad_color)
    
    # Center the resized image
    paste_x = (target_width - new_width) // 2
    paste_y = (target_height - new_height) // 2
    
    new_image.paste(resized_image, (paste_x, paste_y))
    
    return np.array(new_image)

def resize_with_stretch(image, target_size=(224, 224)):
    """Resize dengan stretching"""
    if isinstance(image, np.ndarray):
        image = Image.fromarray(image)
    
    resized_image = image.resize(target_size, Image.LANCZOS)
    return np.array(resized_image)

def create_mobilenetv2_model(num_classes=4, input_shape=(224, 224, 3)):
    """
    Membuat model MobileNetV2 untuk klasifikasi burung
    """
    print("🏗️  Membuat model MobileNetV2...")
    
    # Load MobileNetV2 as base model
    base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)
    
    # Freeze layers in base model
    for layer in base_model.layers:
        layer.trainable = False
    
    # Add custom head
    x = base_model.output
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.5)(x)
    x = Dense(1024, activation='relu')(x)
    x = Dropout(0.5)(x)
    predictions = Dense(num_classes, activation='softmax')(x)
    
    # Combine base model and custom head
    model = Model(inputs=base_model.input, outputs=predictions)
    
    # Fine-tune by unfreezing some layers
    for layer in base_model.layers[:-10]:
        layer.trainable = False
    
    # Learning rate scheduling
    lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
        initial_learning_rate=1e-4,
        decay_steps=1000,
        decay_rate=0.9
    )
    optimizer = Adam(learning_rate=lr_schedule)
    
    # Compile model
    model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])
    
    print(f"✅ Model created with {model.count_params():,} parameters")
    return model

def setup_data_generators(data_dir, batch_size=32, validation_split=0.3):
    """
    Setup data generators untuk training dan validation
    """
    print("📊 Setting up data generators...")
    
    # Data augmentation untuk training
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=True,
        fill_mode='nearest',
        validation_split=validation_split
    )
    
    # Hanya rescaling untuk validation
    valid_datagen = ImageDataGenerator(
        rescale=1./255,
        validation_split=validation_split
    )
    
    # Create generators
    train_generator = train_datagen.flow_from_directory(
        data_dir,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=True,
        subset='training'
    )
    
    valid_generator = valid_datagen.flow_from_directory(
        data_dir,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=False,
        subset='validation'
    )
    
    print(f"✅ Training samples: {train_generator.samples}")
    print(f"✅ Validation samples: {valid_generator.samples}")
    print(f"✅ Classes: {list(train_generator.class_indices.keys())}")
    
    return train_generator, valid_generator

# Main execution
if __name__ == "__main__":
    print("🐦 MobileNetV2 Bird Classification dengan Preprocessing")
    print("=" * 60)
    
    # Konfigurasi paths
    raw_data_dir = "./raw_data"  # Direktori gambar asli
    processed_data_dir = "./processed_data"  # Direktori gambar yang sudah diproses
    
    # Konfigurasi model
    INPUT_SHAPE = (224, 224, 3)
    NUM_CLASSES = 4
    BATCH_SIZE = 32
    EPOCHS = 30
    
    # Step 1: Preprocessing gambar
    if os.path.exists(raw_data_dir):
        print("🔄 Step 1: Preprocessing gambar...")
        
        # Pilih metode preprocessing
        method = 'padding'  # atau 'stretch'
        
        class_counts = preprocess_images_for_training(
            raw_data_dir, 
            processed_data_dir, 
            target_size=(224, 224),
            method=method
        )
        
        # Step 2: Setup data generators
        print("\n🔄 Step 2: Setup data generators...")
        train_gen, valid_gen = setup_data_generators(
            processed_data_dir, 
            batch_size=BATCH_SIZE
        )
        
        # Step 3: Create model
        print("\n🔄 Step 3: Create MobileNetV2 model...")
        model = create_mobilenetv2_model(NUM_CLASSES, INPUT_SHAPE)
        
        # Step 4: Training
        print("\n🔄 Step 4: Training model...")
        
        # Early stopping callback
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=5,
            restore_best_weights=True
        )
        
        # Model checkpoint callback
        checkpoint = tf.keras.callbacks.ModelCheckpoint(
            'best_mobilenetv2_birds.h5',
            monitor='val_accuracy',
            save_best_only=True,
            mode='max'
        )
        
        # Train model
        history = model.fit(
            train_gen,
            epochs=EPOCHS,
            validation_data=valid_gen,
            callbacks=[early_stopping, checkpoint],
            verbose=1
        )
        
        # Step 5: Evaluate model
        print("\n🔄 Step 5: Evaluating model...")
        test_loss, test_accuracy = model.evaluate(valid_gen)
        print(f"📊 Test Loss: {test_loss:.4f}")
        print(f"📊 Test Accuracy: {test_accuracy:.4f}")
        
        # Save final model
        model.save('mobilenetv2_birds_final.h5')
        print("💾 Model saved as 'mobilenetv2_birds_final.h5'")
        
        print("\n🎉 Training completed successfully!")
        
    else:
        print(f"❌ Raw data directory '{raw_data_dir}' tidak ditemukan!")
        print("Silakan buat direktori dengan struktur:")
        print("raw_data/")
        print("  ├── Eurasian Tree Sparrow - Passer montanus/")
        print("  ├── Javan Munia - Lonchura leucogastroides/")
        print("  ├── Scaly-breasted Munia - Lonchura punctulata/")
        print("  └── White-headed Munia - Lonchura maja/")
