#!/usr/bin/env python3
"""
Run Dataset Split - Script untuk menjalankan pembagian dataset dengan mudah
Script wrapper untuk dataset_splitter.py dengan opsi yang lebih user-friendly
"""

import os
import sys
from pathlib import Path
from dataset_splitter import create_train_val_split, analyze_dataset
from dataset_utils import print_dataset_summary, validate_dataset_structure

def check_source_dataset():
    """
    Cek keberadaan dan struktur source dataset
    """
    source_path = Path("ResizedDataset/Dataset")
    
    if not source_path.exists():
        print("❌ Source dataset tidak ditemukan!")
        print(f"   Expected path: {source_path.absolute()}")
        print("\n💡 Pastikan Anda berada di direktori yang benar dan")
        print("   folder 'ResizedDataset/Dataset' sudah ada.")
        return False
    
    # Cek subfolder
    subfolders = [f for f in source_path.iterdir() if f.is_dir()]
    
    if not subfolders:
        print("❌ Tidak ada subfolder dalam source dataset!")
        print(f"   Path: {source_path.absolute()}")
        return False
    
    print(f"✅ Source dataset ditemukan: {source_path.absolute()}")
    print(f"📁 Ditemukan {len(subfolders)} kelas:")
    
    total_files = 0
    for folder in sorted(subfolders, key=lambda x: x.name):
        files = [f for f in folder.iterdir() if f.is_file()]
        file_count = len(files)
        total_files += file_count
        print(f"   📁 {folder.name}: {file_count:,} files")
    
    print(f"📊 Total files: {total_files:,}")
    return True

def run_standard_split():
    """
    Jalankan pembagian dataset standar (70:30)
    """
    print("🔄 PEMBAGIAN DATASET STANDAR (70:30)")
    print("=" * 60)
    
    if not check_source_dataset():
        return False
    
    source_dir = "ResizedDataset/Dataset"
    output_dir = "SplitDataset"
    
    print(f"\n📋 Parameter:")
    print(f"   📁 Source: {source_dir}")
    print(f"   📁 Output: {output_dir}")
    print(f"   📊 Ratio: 70% training, 30% validation")
    print(f"   🎲 Seed: 42 (untuk konsistensi)")
    
    # Preview dulu
    print(f"\n🔍 PREVIEW PEMBAGIAN:")
    print("-" * 40)
    
    result = create_train_val_split(source_dir, output_dir, 0.7, 42, preview=True)
    
    if not result:
        print("❌ Gagal melakukan preview!")
        return False
    
    # Konfirmasi
    print(f"\n" + "="*60)
    confirm = input("❓ Lanjutkan dengan pembagian sesungguhnya? (y/n): ").strip().lower()
    
    if confirm not in ['y', 'yes', 'ya']:
        print("❌ Pembagian dataset dibatalkan.")
        return False
    
    # Jalankan pembagian
    print(f"\n🔄 MEMULAI PEMBAGIAN DATASET:")
    print("-" * 40)
    
    result = create_train_val_split(source_dir, output_dir, 0.7, 42, preview=False)
    
    if result:
        print(f"\n🎉 PEMBAGIAN DATASET BERHASIL!")
        print(f"   📁 Output directory: {Path(output_dir).absolute()}")
        
        # Analisis hasil
        print(f"\n📊 ANALISIS HASIL:")
        print("-" * 30)
        print_dataset_summary(output_dir)
        
        return True
    else:
        print("❌ Gagal melakukan pembagian dataset!")
        return False

def run_custom_split():
    """
    Jalankan pembagian dataset dengan parameter custom
    """
    print("⚙️  PEMBAGIAN DATASET CUSTOM")
    print("=" * 60)
    
    if not check_source_dataset():
        return False
    
    # Input parameter
    print(f"\n📋 INPUT PARAMETER:")
    
    # Train ratio
    while True:
        try:
            ratio_input = input("📊 Train ratio (0.1-0.9, default: 0.7): ").strip()
            train_ratio = float(ratio_input) if ratio_input else 0.7
            
            if 0.1 <= train_ratio <= 0.9:
                break
            else:
                print("⚠️  Train ratio harus antara 0.1 dan 0.9!")
        except ValueError:
            print("⚠️  Input tidak valid! Masukkan angka desimal.")
    
    # Output directory
    output_input = input("📁 Output directory (default: SplitDataset): ").strip()
    output_dir = output_input if output_input else "SplitDataset"
    
    # Random seed
    while True:
        try:
            seed_input = input("🎲 Random seed (default: 42): ").strip()
            random_seed = int(seed_input) if seed_input else 42
            break
        except ValueError:
            print("⚠️  Seed harus berupa angka!")
    
    source_dir = "ResizedDataset/Dataset"
    
    print(f"\n📋 Parameter yang akan digunakan:")
    print(f"   📁 Source: {source_dir}")
    print(f"   📁 Output: {output_dir}")
    print(f"   📊 Ratio: {train_ratio*100:.0f}% training, {(1-train_ratio)*100:.0f}% validation")
    print(f"   🎲 Seed: {random_seed}")
    
    # Preview
    print(f"\n🔍 PREVIEW PEMBAGIAN:")
    print("-" * 40)
    
    result = create_train_val_split(source_dir, output_dir, train_ratio, random_seed, preview=True)
    
    if not result:
        print("❌ Gagal melakukan preview!")
        return False
    
    # Konfirmasi
    print(f"\n" + "="*60)
    confirm = input("❓ Lanjutkan dengan pembagian sesungguhnya? (y/n): ").strip().lower()
    
    if confirm not in ['y', 'yes', 'ya']:
        print("❌ Pembagian dataset dibatalkan.")
        return False
    
    # Jalankan pembagian
    print(f"\n🔄 MEMULAI PEMBAGIAN DATASET:")
    print("-" * 40)
    
    result = create_train_val_split(source_dir, output_dir, train_ratio, random_seed, preview=False)
    
    if result:
        print(f"\n🎉 PEMBAGIAN DATASET BERHASIL!")
        print(f"   📁 Output directory: {Path(output_dir).absolute()}")
        
        # Analisis hasil
        print(f"\n📊 ANALISIS HASIL:")
        print("-" * 30)
        print_dataset_summary(output_dir)
        
        return True
    else:
        print("❌ Gagal melakukan pembagian dataset!")
        return False

def analyze_existing_dataset():
    """
    Analisis dataset yang sudah ada
    """
    print("📊 ANALISIS DATASET")
    print("=" * 60)
    
    # Input dataset directory
    dataset_input = input("📁 Dataset directory (default: SplitDataset): ").strip()
    dataset_dir = dataset_input if dataset_input else "SplitDataset"
    
    dataset_path = Path(dataset_dir)
    
    if not dataset_path.exists():
        print(f"❌ Dataset directory '{dataset_dir}' tidak ditemukan!")
        return False
    
    # Validasi struktur
    print(f"\n🔍 VALIDASI STRUKTUR:")
    print("-" * 30)
    
    is_valid, result = validate_dataset_structure(dataset_dir)
    
    if is_valid:
        print("✅ Dataset structure: VALID")
    else:
        print("❌ Dataset structure: INVALID")
        if isinstance(result, dict):
            for issue in result.get("issues", []):
                print(f"   ❌ {issue}")
            for warning in result.get("warnings", []):
                print(f"   ⚠️  {warning}")
        return False
    
    # Analisis detail
    print(f"\n📊 ANALISIS DETAIL:")
    print("-" * 30)
    print_dataset_summary(dataset_dir)
    
    return True

def main():
    print("🚀 RUN DATASET SPLIT")
    print("=" * 60)
    print("Script untuk membagi dataset ResizedDataset/Dataset menjadi")
    print("training dan validation dengan proporsi 70:30 atau custom")
    print()
    
    print("Pilih opsi:")
    print("1. 📊 Pembagian Standar (70:30, seed=42)")
    print("2. ⚙️  Pembagian Custom (ratio dan parameter custom)")
    print("3. 📈 Analisis Dataset yang sudah ada")
    print("4. 🔍 Cek Source Dataset")
    print("5. ❌ Keluar")
    print()
    
    try:
        choice = int(input("Pilih opsi (1-5): "))
        
        if choice == 1:
            run_standard_split()
        
        elif choice == 2:
            run_custom_split()
        
        elif choice == 3:
            analyze_existing_dataset()
        
        elif choice == 4:
            check_source_dataset()
        
        elif choice == 5:
            print("👋 Terima kasih!")
            return
        
        else:
            print("❌ Pilihan tidak valid!")
    
    except (ValueError, KeyboardInterrupt):
        print("\n❌ Program dibatalkan.")

if __name__ == "__main__":
    main()
