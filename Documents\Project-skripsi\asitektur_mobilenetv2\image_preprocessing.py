#!/usr/bin/env python3
"""
Image preprocessing script untuk konversi shape gambar menjadi 224x224
sebelum digunakan dalam data generator untuk MobileNetV2
"""

import numpy as np
import cv2
from PIL import Image
import os
import matplotlib.pyplot as plt
from pathlib import Path

def resize_with_padding(image, target_size=(224, 224), pad_color=(0, 0, 0)):
    """
    Resize gambar dengan padding untuk mempertahankan aspect ratio
    
    Args:
        image: numpy array atau PIL Image
        target_size: tuple (width, height) target size
        pad_color: tuple (R, G, B) warna padding
    
    Returns:
        numpy array gambar yang sudah diresize dengan padding
    """
    if isinstance(image, np.ndarray):
        # Convert numpy array to PIL Image
        if len(image.shape) == 3:
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            image = Image.fromarray(image)
    
    # Get original dimensions
    original_width, original_height = image.size
    target_width, target_height = target_size
    
    # Calculate scaling factor to fit image within target size
    scale = min(target_width / original_width, target_height / original_height)
    
    # Calculate new dimensions
    new_width = int(original_width * scale)
    new_height = int(original_height * scale)
    
    # Resize image
    resized_image = image.resize((new_width, new_height), Image.LANCZOS)
    
    # Create new image with target size and padding color
    new_image = Image.new('RGB', target_size, pad_color)
    
    # Calculate position to paste resized image (center it)
    paste_x = (target_width - new_width) // 2
    paste_y = (target_height - new_height) // 2
    
    # Paste resized image onto padded background
    new_image.paste(resized_image, (paste_x, paste_y))
    
    return np.array(new_image)

def resize_with_stretch(image, target_size=(224, 224)):
    """
    Resize gambar dengan stretching (tanpa mempertahankan aspect ratio)
    
    Args:
        image: numpy array atau PIL Image
        target_size: tuple (width, height) target size
    
    Returns:
        numpy array gambar yang sudah diresize
    """
    if isinstance(image, np.ndarray):
        # Convert numpy array to PIL Image
        if len(image.shape) == 3:
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            image = Image.fromarray(image)
    
    # Resize dengan LANCZOS untuk kualitas terbaik
    resized_image = image.resize(target_size, Image.LANCZOS)
    
    return np.array(resized_image)

def preprocess_dataset(input_dir, output_dir, target_size=(224, 224), method='padding'):
    """
    Preprocess seluruh dataset dengan mengkonversi semua gambar ke target size
    
    Args:
        input_dir: path ke direktori input yang berisi subdirektori kelas
        output_dir: path ke direktori output
        target_size: tuple (width, height) target size
        method: 'padding' atau 'stretch'
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directory if it doesn't exist
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Supported image extensions
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    total_processed = 0
    
    # Process each class directory
    for class_dir in input_path.iterdir():
        if class_dir.is_dir():
            print(f"Processing class: {class_dir.name}")
            
            # Create output class directory
            output_class_dir = output_path / class_dir.name
            output_class_dir.mkdir(exist_ok=True)
            
            # Process each image in the class directory
            image_files = [f for f in class_dir.iterdir() 
                          if f.suffix.lower() in image_extensions]
            
            for i, image_file in enumerate(image_files):
                try:
                    # Load image
                    image = cv2.imread(str(image_file))
                    if image is None:
                        print(f"Warning: Could not load {image_file}")
                        continue
                    
                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    # Apply preprocessing method
                    if method == 'padding':
                        processed_image = resize_with_padding(image, target_size)
                    else:  # stretch
                        processed_image = resize_with_stretch(image, target_size)
                    
                    # Save processed image
                    output_file = output_class_dir / f"{image_file.stem}_processed{image_file.suffix}"
                    
                    # Convert back to BGR for saving with cv2
                    processed_image_bgr = cv2.cvtColor(processed_image, cv2.COLOR_RGB2BGR)
                    cv2.imwrite(str(output_file), processed_image_bgr)
                    
                    total_processed += 1
                    
                    if (i + 1) % 10 == 0:
                        print(f"  Processed {i + 1}/{len(image_files)} images")
                        
                except Exception as e:
                    print(f"Error processing {image_file}: {e}")
            
            print(f"  Completed class {class_dir.name}: {len(image_files)} images")
    
    print(f"\nTotal images processed: {total_processed}")
    print(f"Processed dataset saved to: {output_dir}")

def visualize_preprocessing_comparison(image_path, target_size=(224, 224)):
    """
    Visualisasi perbandingan metode preprocessing
    
    Args:
        image_path: path ke gambar untuk divisualisasikan
        target_size: tuple (width, height) target size
    """
    # Load original image
    original = cv2.imread(image_path)
    if original is None:
        print(f"Could not load image: {image_path}")
        return
    
    original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
    
    # Apply different preprocessing methods
    padded = resize_with_padding(original_rgb, target_size)
    stretched = resize_with_stretch(original_rgb, target_size)
    
    # Create visualization
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(original_rgb)
    axes[0].set_title(f'Original\nSize: {original_rgb.shape[:2]}')
    axes[0].axis('off')
    
    axes[1].imshow(padded)
    axes[1].set_title(f'With Padding\nSize: {padded.shape[:2]}')
    axes[1].axis('off')
    
    axes[2].imshow(stretched)
    axes[2].set_title(f'Stretched\nSize: {stretched.shape[:2]}')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()

# Example usage
if __name__ == "__main__":
    print("Image Preprocessing untuk MobileNetV2 (224x224)")
    print("=" * 50)
    
    # Example paths - sesuaikan dengan struktur direktori Anda
    input_directory = "./raw_data"  # Direktori dengan gambar asli
    output_directory = "./processed_data"  # Direktori untuk gambar yang sudah diproses
    
    print(f"Input directory: {input_directory}")
    print(f"Output directory: {output_directory}")
    print(f"Target size: 224x224")
    
    # Check if input directory exists
    if not os.path.exists(input_directory):
        print(f"\n⚠️  Input directory '{input_directory}' tidak ditemukan!")
        print("Silakan buat direktori dengan struktur berikut:")
        print("raw_data/")
        print("  ├── Eurasian Tree Sparrow - Passer montanus/")
        print("  ├── Javan Munia - Lonchura leucogastroides/")
        print("  ├── Scaly-breasted Munia - Lonchura punctulata/")
        print("  └── White-headed Munia - Lonchura maja/")
        print("\nSetiap subdirektori harus berisi gambar dari spesies burung yang sesuai.")
    else:
        print(f"\n✓ Input directory ditemukan!")
        
        # Pilih metode preprocessing
        method = input("\nPilih metode preprocessing (padding/stretch) [padding]: ").strip().lower()
        if method not in ['padding', 'stretch']:
            method = 'padding'
        
        print(f"Menggunakan metode: {method}")
        
        # Process dataset
        preprocess_dataset(input_directory, output_directory, method=method)
        
        print("\n✅ Preprocessing selesai!")
        print(f"Dataset yang sudah diproses tersimpan di: {output_directory}")
