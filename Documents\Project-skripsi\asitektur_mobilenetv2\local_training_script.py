#!/usr/bin/env python3
"""
Script untuk training MobileNetV2 dengan data lokal
Menggunakan data yang sudah ada di direktori Dataset atau ResizedDataset
"""

import tensorflow as tf
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import os
from pathlib import Path

def setup_data_paths():
    """
    Setup path data untuk environment lokal
    """
    print("🔍 Mencari direktori data...")
    
    # Prioritas direktori data (dari yang paling diutamakan)
    data_options = [
        './ResizedDataset',  # Data yang sudah diproses ke 224x224
        './Dataset',         # Data asli
        './data',           # Direktori alternatif
        './processed_data'  # Direktori hasil preprocessing
    ]
    
    selected_data_dir = None
    
    for data_dir in data_options:
        if os.path.exists(data_dir):
            # Cek apakah ada subdirectory dengan gambar
            subdirs = [d for d in os.listdir(data_dir) 
                      if os.path.isdir(os.path.join(data_dir, d))]
            
            if subdirs:
                selected_data_dir = data_dir
                print(f"✅ Menggunakan data dari: {data_dir}")
                
                # Tampilkan info dataset
                print(f"\n📊 Dataset info:")
                total_images = 0
                for subdir in subdirs:
                    subdir_path = os.path.join(data_dir, subdir)
                    if os.path.isdir(subdir_path):
                        image_files = [f for f in os.listdir(subdir_path) 
                                     if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                        image_count = len(image_files)
                        total_images += image_count
                        print(f"  📂 {subdir}: {image_count} images")
                
                print(f"📈 Total images: {total_images}")
                break
    
    if not selected_data_dir:
        print("❌ Tidak ditemukan direktori data yang valid!")
        print("\n📋 Silakan pastikan salah satu direktori berikut ada dan berisi data:")
        for option in data_options:
            print(f"  - {option}")
        print("\nSetiap direktori harus berisi subdirektori untuk setiap kelas burung.")
        return None
    
    return selected_data_dir

def create_data_generators(data_dir, batch_size=32, validation_split=0.3):
    """
    Membuat data generators untuk training dan validation
    """
    print(f"\n🔄 Membuat data generators...")
    print(f"📁 Data directory: {data_dir}")
    print(f"🔧 Batch size: {batch_size}")
    print(f"📊 Validation split: {validation_split}")
    
    # Data generator untuk training dengan augmentasi
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=True,
        fill_mode='nearest',
        validation_split=validation_split
    )
    
    # Data generator untuk validation (hanya rescaling)
    valid_datagen = ImageDataGenerator(
        rescale=1./255,
        validation_split=validation_split
    )
    
    # Membuat generators
    train_generator = train_datagen.flow_from_directory(
        data_dir,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=True,
        subset="training"
    )
    
    valid_generator = valid_datagen.flow_from_directory(
        data_dir,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=False,
        subset="validation"
    )
    
    print(f"✅ Data generators created!")
    print(f"📊 Training samples: {train_generator.samples}")
    print(f"📊 Validation samples: {valid_generator.samples}")
    print(f"📊 Number of classes: {train_generator.num_classes}")
    print(f"📊 Class indices: {train_generator.class_indices}")
    
    return train_generator, valid_generator

def create_mobilenetv2_model(num_classes):
    """
    Membuat model MobileNetV2 dengan custom head
    """
    print(f"\n🏗️  Membuat MobileNetV2 model...")
    print(f"🎯 Number of classes: {num_classes}")
    
    # Input shape untuk MobileNetV2
    input_shape = (224, 224, 3)
    
    # Load MobileNetV2 sebagai base model
    base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)
    
    # Freeze layers di base model
    for layer in base_model.layers:
        layer.trainable = False
    
    # Tambahkan custom head
    x = base_model.output
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.5)(x)
    x = Dense(1024, activation='relu')(x)
    x = Dropout(0.5)(x)
    predictions = Dense(num_classes, activation='softmax')(x)
    
    # Gabungkan base model dan custom head
    model = Model(inputs=base_model.input, outputs=predictions)
    
    # Fine-tune dengan unfreeze beberapa layer terakhir
    for layer in base_model.layers[:-10]:
        layer.trainable = False
    
    # Setup learning rate scheduling
    lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
        initial_learning_rate=1e-4,
        decay_steps=1000,
        decay_rate=0.9
    )
    optimizer = Adam(learning_rate=lr_schedule)
    
    # Compile model
    model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])
    
    print(f"✅ Model created and compiled!")
    print(f"📊 Total parameters: {model.count_params():,}")
    
    return model

def train_model(model, train_generator, valid_generator, epochs=30):
    """
    Training model dengan early stopping
    """
    print(f"\n🚀 Memulai training...")
    print(f"📊 Epochs: {epochs}")
    
    # Setup early stopping
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=5,
        restore_best_weights=True,
        verbose=1
    )
    
    # Setup model checkpoint
    checkpoint = tf.keras.callbacks.ModelCheckpoint(
        'best_model.h5',
        monitor='val_accuracy',
        save_best_only=True,
        verbose=1
    )
    
    # Training
    history = model.fit(
        train_generator,
        epochs=epochs,
        validation_data=valid_generator,
        callbacks=[early_stopping, checkpoint],
        verbose=1
    )
    
    print("✅ Training selesai!")
    return history

def evaluate_model(model, valid_generator):
    """
    Evaluasi model dan tampilkan hasil
    """
    print(f"\n📊 Evaluating model...")
    
    # Evaluasi
    test_loss, test_accuracy = model.evaluate(valid_generator, verbose=1)
    print(f"📈 Test Loss: {test_loss:.4f}")
    print(f"📈 Test Accuracy: {test_accuracy:.4f}")
    
    return test_loss, test_accuracy

def plot_training_history(history):
    """
    Plot training history
    """
    print(f"\n📊 Plotting training history...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # Plot accuracy
    ax1.plot(history.history['accuracy'], label='Training Accuracy')
    ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')
    ax1.set_title('Model Accuracy')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    ax1.grid(True)
    
    # Plot loss
    ax2.plot(history.history['loss'], label='Training Loss')
    ax2.plot(history.history['val_loss'], label='Validation Loss')
    ax2.set_title('Model Loss')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Training history plot saved as 'training_history.png'")

def main():
    """
    Main function untuk menjalankan seluruh proses training
    """
    print("="*60)
    print("🐦 MOBILENETV2 BIRD CLASSIFICATION - LOCAL TRAINING")
    print("="*60)
    
    # Setup data paths
    data_dir = setup_data_paths()
    if not data_dir:
        return
    
    # Create data generators
    train_generator, valid_generator = create_data_generators(data_dir)
    
    # Get number of classes
    num_classes = train_generator.num_classes
    
    # Create model
    model = create_mobilenetv2_model(num_classes)
    
    # Train model
    history = train_model(model, train_generator, valid_generator)
    
    # Evaluate model
    evaluate_model(model, valid_generator)
    
    # Plot training history
    plot_training_history(history)
    
    # Save final model
    model.save('birds_classification_final.h5')
    print(f"\n✅ Model saved as 'birds_classification_final.h5'")
    
    print("\n" + "="*60)
    print("🎉 TRAINING COMPLETED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()
