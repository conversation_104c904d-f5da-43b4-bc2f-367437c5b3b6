#!/usr/bin/env python3
"""
Dataset Utilities - Utilitas untuk Dataset Management
Script tambahan untuk validasi, analisis, dan management dataset
"""

import os
from pathlib import Path
import shutil
from collections import defaultdict, Counter
import json
from datetime import datetime

def validate_dataset_structure(dataset_dir):
    """
    Validasi struktur dataset
    """
    dataset_path = Path(dataset_dir)
    
    if not dataset_path.exists():
        return False, f"Dataset directory '{dataset_dir}' tidak ditemukan"
    
    issues = []
    warnings = []
    
    # Cek struktur train/validation
    train_dir = dataset_path / "train"
    val_dir = dataset_path / "validation"
    
    if not train_dir.exists():
        issues.append("Train directory tidak ditemukan")
    
    if not val_dir.exists():
        issues.append("Validation directory tidak ditemukan")
    
    if issues:
        return False, issues
    
    # Cek konsistensi kelas
    train_classes = set(f.name for f in train_dir.iterdir() if f.is_dir())
    val_classes = set(f.name for f in val_dir.iterdir() if f.is_dir())
    
    if train_classes != val_classes:
        missing_in_train = val_classes - train_classes
        missing_in_val = train_classes - val_classes
        
        if missing_in_train:
            issues.append(f"Kelas tidak ada di train: {missing_in_train}")
        if missing_in_val:
            issues.append(f"Kelas tidak ada di validation: {missing_in_val}")
    
    # Cek file kosong
    for class_name in train_classes:
        train_files = list((train_dir / class_name).glob("*"))
        val_files = list((val_dir / class_name).glob("*"))
        
        train_count = len([f for f in train_files if f.is_file()])
        val_count = len([f for f in val_files if f.is_file()])
        
        if train_count == 0:
            warnings.append(f"Kelas '{class_name}' kosong di train")
        if val_count == 0:
            warnings.append(f"Kelas '{class_name}' kosong di validation")
    
    return len(issues) == 0, {"issues": issues, "warnings": warnings}

def generate_dataset_report(dataset_dir, output_file=None):
    """
    Generate laporan lengkap dataset
    """
    dataset_path = Path(dataset_dir)
    
    if not dataset_path.exists():
        print(f"❌ Dataset directory '{dataset_dir}' tidak ditemukan!")
        return None
    
    report = {
        "dataset_info": {
            "name": dataset_path.name,
            "path": str(dataset_path.absolute()),
            "generated_at": datetime.now().isoformat(),
        },
        "structure": {},
        "statistics": {},
        "validation": {}
    }
    
    # Validasi struktur
    is_valid, validation_result = validate_dataset_structure(dataset_dir)
    report["validation"]["is_valid"] = is_valid
    report["validation"]["details"] = validation_result
    
    if not is_valid:
        print("❌ Dataset structure tidak valid!")
        return report
    
    train_dir = dataset_path / "train"
    val_dir = dataset_path / "validation"
    
    # Analisis struktur
    train_stats = {}
    val_stats = {}
    total_train = 0
    total_val = 0
    
    # Analisis train
    for class_dir in train_dir.iterdir():
        if class_dir.is_dir():
            files = [f for f in class_dir.iterdir() if f.is_file()]
            count = len(files)
            train_stats[class_dir.name] = count
            total_train += count
    
    # Analisis validation
    for class_dir in val_dir.iterdir():
        if class_dir.is_dir():
            files = [f for f in class_dir.iterdir() if f.is_file()]
            count = len(files)
            val_stats[class_dir.name] = count
            total_val += count
    
    # Struktur
    report["structure"]["train_classes"] = len(train_stats)
    report["structure"]["val_classes"] = len(val_stats)
    report["structure"]["classes"] = sorted(train_stats.keys())
    
    # Statistik
    report["statistics"]["total_files"] = total_train + total_val
    report["statistics"]["train_files"] = total_train
    report["statistics"]["val_files"] = total_val
    report["statistics"]["train_ratio"] = total_train / (total_train + total_val) if (total_train + total_val) > 0 else 0
    report["statistics"]["val_ratio"] = total_val / (total_train + total_val) if (total_train + total_val) > 0 else 0
    
    # Detail per kelas
    class_details = {}
    for class_name in sorted(train_stats.keys()):
        train_count = train_stats.get(class_name, 0)
        val_count = val_stats.get(class_name, 0)
        total_class = train_count + val_count
        
        class_details[class_name] = {
            "total": total_class,
            "train": train_count,
            "val": val_count,
            "train_ratio": train_count / total_class if total_class > 0 else 0,
            "val_ratio": val_count / total_class if total_class > 0 else 0
        }
    
    report["statistics"]["class_details"] = class_details
    
    # Simpan report jika diminta
    if output_file:
        output_path = Path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"📄 Report disimpan ke: {output_path}")
    
    return report

def print_dataset_summary(dataset_dir):
    """
    Print summary dataset ke console
    """
    report = generate_dataset_report(dataset_dir)
    
    if not report:
        return
    
    print(f"📊 DATASET SUMMARY: {report['dataset_info']['name']}")
    print("=" * 60)
    
    # Validasi
    if report["validation"]["is_valid"]:
        print("✅ Dataset structure: VALID")
    else:
        print("❌ Dataset structure: INVALID")
        if "issues" in report["validation"]["details"]:
            for issue in report["validation"]["details"]["issues"]:
                print(f"   ❌ {issue}")
        if "warnings" in report["validation"]["details"]:
            for warning in report["validation"]["details"]["warnings"]:
                print(f"   ⚠️  {warning}")
        return
    
    # Statistik umum
    stats = report["statistics"]
    print(f"📁 Total Classes: {report['structure']['train_classes']}")
    print(f"📊 Total Files: {stats['total_files']:,}")
    print(f"📚 Training: {stats['train_files']:,} files ({stats['train_ratio']*100:.1f}%)")
    print(f"📝 Validation: {stats['val_files']:,} files ({stats['val_ratio']*100:.1f}%)")
    print()
    
    # Detail per kelas
    print("📋 Detail per kelas:")
    for class_name, details in stats["class_details"].items():
        print(f"  📁 {class_name}: {details['total']:,} total → "
              f"Train: {details['train']:,} ({details['train_ratio']*100:.1f}%), "
              f"Val: {details['val']:,} ({details['val_ratio']*100:.1f}%)")

def cleanup_empty_dirs(dataset_dir):
    """
    Bersihkan direktori kosong
    """
    dataset_path = Path(dataset_dir)
    
    if not dataset_path.exists():
        print(f"❌ Dataset directory '{dataset_dir}' tidak ditemukan!")
        return
    
    empty_dirs = []
    
    for root, dirs, files in os.walk(dataset_path, topdown=False):
        for dir_name in dirs:
            dir_path = Path(root) / dir_name
            if not any(dir_path.iterdir()):  # Direktori kosong
                empty_dirs.append(dir_path)
    
    if empty_dirs:
        print(f"🗑️  Ditemukan {len(empty_dirs)} direktori kosong:")
        for dir_path in empty_dirs:
            print(f"   📁 {dir_path}")
        
        confirm = input("\n❓ Hapus direktori kosong? (y/n): ").strip().lower()
        if confirm in ['y', 'yes', 'ya']:
            for dir_path in empty_dirs:
                try:
                    dir_path.rmdir()
                    print(f"   ✅ Dihapus: {dir_path}")
                except Exception as e:
                    print(f"   ❌ Error: {dir_path} - {e}")
        else:
            print("❌ Cleanup dibatalkan.")
    else:
        print("✅ Tidak ada direktori kosong.")

def compare_datasets(dataset1_dir, dataset2_dir):
    """
    Bandingkan dua dataset
    """
    print(f"🔍 PERBANDINGAN DATASET")
    print("=" * 60)
    
    report1 = generate_dataset_report(dataset1_dir)
    report2 = generate_dataset_report(dataset2_dir)
    
    if not report1 or not report2:
        print("❌ Gagal menganalisis salah satu dataset!")
        return
    
    print(f"📊 Dataset 1: {report1['dataset_info']['name']}")
    print(f"📊 Dataset 2: {report2['dataset_info']['name']}")
    print()
    
    # Perbandingan umum
    stats1 = report1["statistics"]
    stats2 = report2["statistics"]
    
    print("📈 Perbandingan Umum:")
    print(f"   Total Files: {stats1['total_files']:,} vs {stats2['total_files']:,}")
    print(f"   Classes: {report1['structure']['train_classes']} vs {report2['structure']['train_classes']}")
    print(f"   Train Ratio: {stats1['train_ratio']*100:.1f}% vs {stats2['train_ratio']*100:.1f}%")
    print()
    
    # Perbandingan kelas
    classes1 = set(report1["structure"]["classes"])
    classes2 = set(report2["structure"]["classes"])
    
    common_classes = classes1 & classes2
    only_in_1 = classes1 - classes2
    only_in_2 = classes2 - classes1
    
    print("📋 Perbandingan Kelas:")
    print(f"   Common classes: {len(common_classes)}")
    if only_in_1:
        print(f"   Only in dataset 1: {sorted(only_in_1)}")
    if only_in_2:
        print(f"   Only in dataset 2: {sorted(only_in_2)}")
    
    # Detail kelas yang sama
    if common_classes:
        print(f"\n📊 Detail kelas yang sama:")
        for class_name in sorted(common_classes):
            details1 = stats1["class_details"][class_name]
            details2 = stats2["class_details"][class_name]
            
            print(f"   📁 {class_name}: {details1['total']:,} vs {details2['total']:,} files")

def main():
    print("🛠️  DATASET UTILITIES")
    print("=" * 50)
    print("1. Validate dataset structure")
    print("2. Generate dataset report")
    print("3. Print dataset summary")
    print("4. Cleanup empty directories")
    print("5. Compare two datasets")
    print()
    
    try:
        choice = int(input("Pilih opsi (1-5): "))
        
        if choice == 1:
            dataset_dir = input("📁 Dataset directory: ").strip()
            is_valid, result = validate_dataset_structure(dataset_dir)
            
            if is_valid:
                print("✅ Dataset structure VALID")
            else:
                print("❌ Dataset structure INVALID")
                if isinstance(result, dict):
                    for issue in result.get("issues", []):
                        print(f"   ❌ {issue}")
                    for warning in result.get("warnings", []):
                        print(f"   ⚠️  {warning}")
                else:
                    print(f"   {result}")
        
        elif choice == 2:
            dataset_dir = input("📁 Dataset directory: ").strip()
            output_file = input("📄 Output file (optional): ").strip()
            output_file = output_file if output_file else None
            
            report = generate_dataset_report(dataset_dir, output_file)
            if report:
                print("✅ Report generated successfully!")
        
        elif choice == 3:
            dataset_dir = input("📁 Dataset directory: ").strip()
            print_dataset_summary(dataset_dir)
        
        elif choice == 4:
            dataset_dir = input("📁 Dataset directory: ").strip()
            cleanup_empty_dirs(dataset_dir)
        
        elif choice == 5:
            dataset1_dir = input("📁 Dataset 1 directory: ").strip()
            dataset2_dir = input("📁 Dataset 2 directory: ").strip()
            compare_datasets(dataset1_dir, dataset2_dir)
        
        else:
            print("❌ Pilihan tidak valid!")
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ Program dibatalkan.")

if __name__ == "__main__":
    main()
