#!/usr/bin/env python3
"""
Test script untuk menguji fungsi preprocessing yang sudah ditambahkan ke notebook
"""

import tensorflow as tf
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import numpy as np
import matplotlib.pyplot as plt
import os
import cv2
from PIL import Image
from pathlib import Path

# Import fungsi preprocessing dari notebook
def resize_with_padding(image, target_size=(224, 224), pad_color=(0, 0, 0)):
    """
    Resize gambar dengan padding untuk mempertahankan aspect ratio
    """
    if isinstance(image, np.ndarray):
        if len(image.shape) == 3:
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            image = Image.fromarray(image)
    
    original_width, original_height = image.size
    target_width, target_height = target_size
    
    scale = min(target_width / original_width, target_height / original_height)
    
    new_width = int(original_width * scale)
    new_height = int(original_height * scale)
    
    resized_image = image.resize((new_width, new_height), Image.LANCZOS)
    
    new_image = Image.new('RGB', target_size, pad_color)
    
    paste_x = (target_width - new_width) // 2
    paste_y = (target_height - new_height) // 2
    
    new_image.paste(resized_image, (paste_x, paste_y))
    
    return np.array(new_image)

def create_sample_data():
    """
    Membuat sample data untuk testing
    """
    print("🔧 Membuat sample data untuk testing...")
    
    # Create sample directory structure
    raw_data_dir = "./raw_data"
    os.makedirs(raw_data_dir, exist_ok=True)
    
    # Create class directories
    classes = [
        "Eurasian Tree Sparrow - Passer montanus",
        "Javan Munia - Lonchura leucogastroides", 
        "Scaly-breasted Munia - Lonchura punctulata",
        "White-headed Munia - Lonchura maja"
    ]
    
    for class_name in classes:
        class_dir = os.path.join(raw_data_dir, class_name)
        os.makedirs(class_dir, exist_ok=True)
        
        # Create sample images with different sizes
        for i in range(3):
            # Create random colored image with different sizes
            if i == 0:
                size = (150, 200)  # Portrait
            elif i == 1:
                size = (300, 180)  # Landscape
            else:
                size = (100, 100)  # Square
            
            # Generate random image
            image = np.random.randint(0, 255, (*size, 3), dtype=np.uint8)
            
            # Add some pattern to make it more realistic
            center_x, center_y = size[0]//2, size[1]//2
            y, x = np.ogrid[:size[1], :size[0]]
            mask = (x - center_x)**2 + (y - center_y)**2 < (min(size)//3)**2
            image[mask] = [255, 255, 0]  # Yellow circle
            
            # Save image
            image_path = os.path.join(class_dir, f"sample_{i+1}.jpg")
            cv2.imwrite(image_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    
    print(f"✅ Sample data created in: {raw_data_dir}")
    return raw_data_dir

def test_preprocessing():
    """
    Test preprocessing functions
    """
    print("🧪 Testing preprocessing functions...")
    
    # Create sample data if not exists
    raw_data_dir = "./raw_data"
    if not os.path.exists(raw_data_dir):
        raw_data_dir = create_sample_data()
    
    # Test preprocessing on one image
    sample_image_path = None
    for class_dir in os.listdir(raw_data_dir):
        class_path = os.path.join(raw_data_dir, class_dir)
        if os.path.isdir(class_path):
            for file in os.listdir(class_path):
                if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    sample_image_path = os.path.join(class_path, file)
                    break
            if sample_image_path:
                break
    
    if sample_image_path:
        print(f"📸 Testing dengan gambar: {sample_image_path}")
        
        # Load image
        image = cv2.imread(sample_image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        print(f"Original size: {image_rgb.shape}")
        
        # Test padding method
        padded_image = resize_with_padding(image_rgb, (224, 224))
        print(f"Padded size: {padded_image.shape}")
        
        # Visualize results
        fig, axes = plt.subplots(1, 2, figsize=(10, 5))
        
        axes[0].imshow(image_rgb)
        axes[0].set_title(f'Original\nSize: {image_rgb.shape[:2]}')
        axes[0].axis('off')
        
        axes[1].imshow(padded_image)
        axes[1].set_title(f'Processed (Padding)\nSize: {padded_image.shape[:2]}')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('preprocessing_test.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ Preprocessing test completed!")
        print("📊 Hasil visualisasi disimpan sebagai 'preprocessing_test.png'")
        
    else:
        print("❌ Tidak ada sample image ditemukan")

def test_data_generator():
    """
    Test data generator dengan data yang sudah diproses
    """
    print("🔄 Testing data generator...")
    
    processed_data_dir = "./processed_data"
    
    if os.path.exists(processed_data_dir):
        # Setup data generators
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            vertical_flip=True,
            fill_mode='nearest',
            validation_split=0.3,
        )
        
        train_generator = train_datagen.flow_from_directory(
            processed_data_dir,
            target_size=(224, 224),
            batch_size=4,
            class_mode='categorical',
            shuffle=True,
            subset='training'
        )
        
        print(f"✅ Data generator created successfully!")
        print(f"📊 Training samples: {train_generator.samples}")
        print(f"📊 Number of classes: {train_generator.num_classes}")
        print(f"📊 Class indices: {train_generator.class_indices}")
        
        # Test getting one batch
        batch_images, batch_labels = next(train_generator)
        print(f"📦 Batch shape: {batch_images.shape}")
        print(f"📦 Labels shape: {batch_labels.shape}")
        
        # Visualize one batch
        fig, axes = plt.subplots(2, 2, figsize=(8, 8))
        axes = axes.ravel()
        
        class_names = list(train_generator.class_indices.keys())
        
        for i in range(min(4, len(batch_images))):
            axes[i].imshow(batch_images[i])
            class_idx = np.argmax(batch_labels[i])
            axes[i].set_title(f'{class_names[class_idx]}\nShape: {batch_images[i].shape}')
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('data_generator_test.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ Data generator test completed!")
        print("📊 Hasil visualisasi disimpan sebagai 'data_generator_test.png'")
        
    else:
        print(f"❌ Processed data directory tidak ditemukan: {processed_data_dir}")
        print("Silakan jalankan preprocessing terlebih dahulu.")

if __name__ == "__main__":
    print("🧪 Testing Preprocessing Integration untuk MobileNetV2")
    print("=" * 60)
    
    # Test 1: Preprocessing functions
    test_preprocessing()
    
    print("\n" + "-" * 60)
    
    # Test 2: Data generator
    test_data_generator()
    
    print("\n" + "=" * 60)
    print("🎉 All tests completed!")
    print("\n📋 Langkah selanjutnya:")
    print("1. Jalankan notebook Klasifikasi_burung_mobilenetv2.ipynb")
    print("2. Pastikan path data sudah sesuai")
    print("3. Jalankan preprocessing jika belum")
    print("4. Lanjutkan dengan training model")
